@php
    $contract = $contractData ?? (isset($contractVersions) && count($contractVersions) > 0 ? $contractVersions[0] : null);
@endphp
@if($contract)
<div class="d-flex justify-content-end gap-2 align-items-center mb-3">
    <button type="button" class="mx-3 btn btn-primary contract-status-btn" data-contract-id="{{ $contract['id'] }}" data-status="in_review" @if($contract['status'] === 'in_review') disabled @endif>
        <i class="fas fa-eye"></i> In Review
    </button>
    <button type="button" class="btn btn-primary contract-status-btn" data-contract-id="{{ $contract['id'] }}" data-status="approved" @if($contract['status'] === 'approved') disabled @endif>
        <i class="fas fa-check"></i> Approve
    </button>
</div>
@endif
<div class="card">
    <div class="card-body">
        {{-- Contract Information Form --}}
        <div class="row mb-4">
            <div class="col-md-12">
                <h6 class="mb-3">Contract Information</h6>
                <form id="contractForm">
                    @csrf
                    <input type="hidden" name="requirement_id" value="{{ $data->id }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="legal_first_name">Legal First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="legal_first_name" name="legal_first_name"
                                       value="{{ $contract['legal_first_name'] ?? '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="legal_last_name">Legal Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="legal_last_name" name="legal_last_name"
                                       value="{{ $contract['legal_last_name'] ?? '' }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ $contract['email'] ?? '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">Phone <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="phone" name="phone"
                                       value="{{ $contract['phone'] ?? '' }}" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {{-- Contract Versions Section --}}
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Contract Versions</h6>
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#uploadContractModal">
                        <i class="fas fa-upload"></i> Upload New Version
                    </button>
                </div>

                @if(isset($contractVersions) && count($contractVersions) > 0)
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Version</th>
                                    <th>Document</th>
                                    <th>Notes</th>
                                    <th>Created At</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($contractVersions as $version)
                                    <tr>
                                        <td>
                                            <span class="badge badge-info">{{ $version['version_number'] ?? 'v1.0' }}</span>
                                        </td>
                                        <td>
                                            @if($version['file_url'])
                                                <a href="{{ generateSignedUrl($version['file_url']) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            @else
                                                <span class="text-muted">No file</span>
                                            @endif
                                        </td>
                                        <td>{{ $version['notes'] ?? '-' }}</td>
                                        <td>{{ $version['created_at'] ? \Carbon\Carbon::parse($version['created_at'])->format('M d, Y H:i') : 'N/A' }}</td>
                                        <td>{{ $version['created_by'] }}</td>
                                        <td>
                                            @if($version['file_url'])
                                                <a href="{{ generateSignedUrl($version['file_url']) }}" target="_blank" class="btn btn-sm btn-success">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-file-contract fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">No Contract Versions Found</h6>
                        <p class="text-muted">No contract documents have been uploaded for this requirement yet.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#uploadContractModal">
                            <i class="fas fa-upload"></i> Upload First Contract
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

{{-- Upload Contract Modal --}}
<div class="modal fade" id="uploadContractModal" tabindex="-1" role="dialog" aria-labelledby="uploadContractModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadContractModalLabel">Upload Contract Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="uploadContractForm" enctype="multipart/form-data">
                <div class="modal-body">
                    @csrf
                    <input type="hidden" name="requirement_id" value="{{ $data->id }}">
                    <input type="hidden" name="contract_id" value="{{ $contract['id'] ?? '' }}">

                    {{-- Copy contract info from main form --}}
                    <input type="hidden" name="legal_first_name" id="modal_legal_first_name">
                    <input type="hidden" name="legal_last_name" id="modal_legal_last_name">
                    <input type="hidden" name="email" id="modal_email">
                    <input type="hidden" name="phone" id="modal_phone">
                    <input type="hidden" name="job_title" value="Contract Administrator">
                    <input type="hidden" name="address" value="N/A">
                    <input type="hidden" name="client_name" value="{{ $data->school->school_name ?? 'N/A' }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="version_number">Version Number</label>
                                <input type="text" class="form-control" name="version_number" placeholder="e.g., v1.1, v2.0">
                                <small class="form-text text-muted">Leave empty to auto-increment</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="document">Contract Document <span class="text-danger">*</span></label>
                                <input type="file" class="form-control-file" name="document" accept=".pdf,.doc,.docx,.txt" required>
                                <small class="form-text text-muted">Max size: 20MB. Formats: PDF, DOC, DOCX, TXT</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Add any notes about this version..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Contract
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
$(document).ready(function() {
    // Handle contract status button clicks
    $('.contract-status-btn').on('click', function() {
        const contractId = $(this).data('contract-id');
        const status = $(this).data('status');

        // Check if updateContractStatus function exists
        if (typeof window.updateContractStatus === 'function') {
            window.updateContractStatus(contractId, status);
        } else {
            console.error('updateContractStatus function not found');
            alertify.error('Contract status update function not available. Please refresh the page.');
        }
    });
});
</script>