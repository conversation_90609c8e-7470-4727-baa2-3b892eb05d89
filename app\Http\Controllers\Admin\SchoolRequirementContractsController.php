<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\SchoolRequirementContract;
use App\SchoolRequirementContractVersion;
use App\SettingTermsModel;
use App\V2\Core\Helpers\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;

class SchoolRequirementContractsController extends Controller
{
    /**
     * Update Contract Information and Content
     * Handles both contract data updates and PDF generation
     */
    public function updateContractContent(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'requirement_id' => 'required|integer|exists:platform_school_requirements,id',
                'contract_content' => 'required|string',
                'legal_first_name' => 'required|string|max:255',
                'legal_last_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $requirementId = $request->requirement_id;
            $contractContent = $request->contract_content;

            DB::beginTransaction();

            // Update the School Contract content in tbl_content_settings
            $schoolContractSetting = SettingTermsModel::where([
                'type' => 'School Contract',
                'locale' => 'en'
            ])->first();

            if ($schoolContractSetting) {
                $schoolContractSetting->update([
                    'description' => $contractContent,
                    'updated_at' => now()
                ]);
            } else {
                // Create new school contract content if it doesn't exist
                SettingTermsModel::create([
                    'type' => 'School Contract',
                    'description' => $contractContent,
                    'locale' => 'en',
                    'status' => 'active'
                ]);
            }

            // Find or create contract for this requirement
            $contract = SchoolRequirementContract::where('requirement_id', $requirementId)->first();

            if (!$contract) {
                $contract = SchoolRequirementContract::create([
                    'requirement_id' => $requirementId,
                    'legal_first_name' => $request->legal_first_name,
                    'legal_last_name' => $request->legal_last_name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'status' => 'draft',
                    'created_by_id' => auth()->user()->id,
                    'created_by_type' => get_class(auth()->user()),
                    'updated_by_id' => auth()->user()->id,
                    'updated_by_type' => get_class(auth()->user()),
                ]);
            } else {
                // Update existing contract with new information
                $contract->update([
                    'legal_first_name' => $request->legal_first_name,
                    'legal_last_name' => $request->legal_last_name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'status' => 'draft',
                    'updated_by_id' => auth()->user()->id,
                    'updated_by_type' => get_class(auth()->user()),
                ]);
            }

            // Generate PDF from content
            $pdfContent = $this->generateContractPDF($contractContent, $requirementId, $contract);

            if ($pdfContent) {
                // Get next version number
                $lastVersion = $contract->versions()->orderBy('created_at', 'desc')->first();
                $versionNumber = 'v1.0';
                if ($lastVersion) {
                    $versionParts = explode('.', str_replace('v', '', $lastVersion->version_number));
                    $majorVersion = intval($versionParts[0] ?? 1);
                    $minorVersion = intval($versionParts[1] ?? 0);
                    $versionNumber = 'v' . $majorVersion . '.' . ($minorVersion + 1);
                }

                // Create new contract version with PDF
                $contractVersion = SchoolRequirementContractVersion::create([
                    'school_requirement_contract_id' => $contract->id,
                    'file_url' => $pdfContent['file_url'],
                    'version_number' => $versionNumber,
                    'notes' => 'Generated from contract content update',
                    'created_by_id' => auth()->user()->id,
                    'created_by_type' => get_class(auth()->user()),
                    'updated_by_id' => auth()->user()->id,
                    'updated_by_type' => get_class(auth()->user()),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Contract information updated and PDF generated successfully',
                'contract' => $contract,
                'version' => $contractVersion ?? null
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating contract content: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update contract content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate PDF from contract content
     */
    private function generateContractPDF($content, $requirementId, $contract)
    {
        try {
            // Prepare contract content with header information
            $contractContent = "
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1>School Contract</h1>
                    <p><strong>Requirement ID:</strong> {$requirementId}</p>
                </div>
                <div style='margin-bottom: 20px;'>
                    <p><strong>Legal Name:</strong> {$contract->legal_first_name} {$contract->legal_last_name}</p>
                    <p><strong>Email:</strong> {$contract->email}</p>
                    <p><strong>Phone:</strong> {$contract->phone}</p>
                    <p><strong>Date:</strong> " . now()->format('F d, Y') . "</p>
                </div>
                <hr style='margin: 20px 0;'>
                <div style='line-height: 1.6;'>
                    {$content}
                </div>
            ";

            // Use DOMPDF to generate PDF with the existing template
            $pdf = Pdf::loadView('web.pdf.agreement_template', [
                'agreementContent' => $contractContent
            ]);

            // Generate filename
            $filename = 'uploads/school/contracts/contract_' . $requirementId . '_' . time() . '.pdf';

            // Save PDF to temporary location
            $pdfFileName = uniqid() . '.pdf';
            $pdfPath = storage_path('app/public/' . $pdfFileName);
            $pdf->save($pdfPath);

            // Create UploadedFile instance for S3 upload
            $uploadedFile = new \Illuminate\Http\UploadedFile($pdfPath, basename($filename), 'application/pdf', null, true);

            // Upload to S3
            $uploadResult = uploads3image($filename, $uploadedFile);

            // Clean up temp file
            if (file_exists($pdfPath)) {
                unlink($pdfPath);
            }

            if ($uploadResult) {
                return [
                    'file_url' => $filename,
                    'success' => true
                ];
            } else {
                throw new Exception('Failed to upload PDF to S3');
            }

        } catch (\Exception $e) {
            Log::error('Error generating contract PDF: ' . $e->getMessage());
            return null;
        }
    }
}
