<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\SchoolRequirementContract;
use App\SchoolRequirementContractVersion;
use App\Models\v1\SchoolUser;
use App\V2\Core\Helpers\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

class SchoolRequirementContractsController extends Controller
{
    /**
     * Get the authenticated school user
     */
    private function getAuthenticatedUser()
    {
        // Try to get user from platform_school guard first
        if (Auth::guard('platform_school')->check()) {
            return Auth::guard('platform_school')->user();
        }

        // Fallback to session-based authentication
        $sessionData = session('schoolloginsession');
        if (!$sessionData || !isset($sessionData['id'])) {
            return null;
        }
        return SchoolUser::find($sessionData['id']);
    }

    /**
     * Check if user is authenticated and return error response if not
     */
    private function checkAuthentication()
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return ApiResponse::error("User not authenticated", 401);
        }
        return null; // No error, user is authenticated
    }
    /**
     * Get all requirement contracts for a school.
     *
     * GET /api/schools/requirements/contracts
     */
    public function index(Request $request)
    {
        try {
            // Check authentication
            $authError = $this->checkAuthentication();
            if ($authError) {
                return $authError;
            }

            $user = $this->getAuthenticatedUser();
            // Get contracts through the requirement relationship since there's no direct school_id
            $contracts = SchoolRequirementContract::with(['requirement.school:id,school_name', 'requirement.subject.subjectArea', 'requirement.classType', 'createdBy', 'updatedBy', 'versions'])
                ->whereHas('requirement', function($query) use ($user) {
                    $query->where('school_id', $user->school_id);
                })
                ->orderBy('created_at', 'desc')
                ->get();

            return ApiResponse::success($contracts, "Contracts fetched successfully");
        } catch (Exception $e) {
            Log::error('Error fetching contracts: ' . $e->getMessage());
            return ApiResponse::error('Failed to fetch contracts', 500);
        }
    }

    /**
     * Get a specific requirement contract for a school.
     *
     * GET /api/schools/requirements/contracts/{id}
     */
    public function show($id)
    {
        try {
            // Check authentication
            $authError = $this->checkAuthentication();
            if ($authError) {
                return $authError;
            }

            $user = $this->getAuthenticatedUser();

            // Find contract with relationships
            $contract = SchoolRequirementContract::with(['requirement.school:id,school_name', 'requirement.subject.subjectArea', 'requirement.classType', 'createdBy', 'updatedBy', 'versions'])
                ->whereHas('requirement', function($query) use ($user) {
                    $query->where('school_id', $user->school_id);
                })
                ->find($id);

            if (!$contract) {
                return ApiResponse::error('Contract not found', 404);
            }

            return ApiResponse::success($contract, "Contract retrieved successfully");

        } catch (Exception $e) {
            Log::error('Error fetching contract: ' . $e->getMessage());
            return ApiResponse::error('Failed to fetch contract', 500);
        }
    }

    /**
     * Create a new requirement contract for a school.
     *
     * POST /api/schools/requirements/contracts
     */
    public function store(Request $request)
    {
        try {
            // Check authentication first
            $authError = $this->checkAuthentication();
            if ($authError) {
                return $authError;
            }
            $user = $this->getAuthenticatedUser();
            DB::beginTransaction();
            // Enhanced validation with better rules
            $validator = Validator::make($request->all(), [
                'requirement_id'      => 'required|exists:platform_school_requirements,id',
                'legal_first_name'    => 'required|string|max:255',
                'legal_last_name'     => 'required|string|max:255',
                'phone'               => 'required|string|max:50',
                'email'               => 'required|email|max:255',
                'job_title'           => 'required|string|max:255',
                'address'             => 'required|string|max:1000',
                'client_name'         => 'required|string|max:255',
                'has_purchase_order'  => 'nullable|boolean',
                'purchase_order_ref'  => 'nullable|string|max:255',
                'status'              => 'nullable|in:draft,pending_approval,in_review,approved,on_hold,cancelled,completed',

                // version table
                'document'            => 'nullable|file|mimes:pdf,doc,docx,txt|max:20480', // 20MB max
                'version_number'      => 'nullable|string|max:50',
                'notes'               => 'nullable|string|max:2000',
            ], [
                'document.mimes' => 'Document must be a PDF, DOC, DOCX, or TXT file.',
                'document.max' => 'Document size must not exceed 20MB.',
                'email.email' => 'Please provide a valid email address.',
                'requirement_id.required' => 'Requirement ID is required.',
                'requirement_id.exists' => 'The selected requirement does not exist.',
                'legal_first_name.required' => 'First name is required.',
                'legal_last_name.required' => 'Last name is required.',
                'phone.required' => 'Phone number is required.',
                'email.required' => 'Email is required.',
                'job_title.required' => 'Job title is required.',
                'address.required' => 'Address is required.',
                'client_name.required' => 'Client name is required.',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error('Validation failed', 422, $validator->errors());
            }

            // Check if contract already exists for this requirement
            $existingContract = SchoolRequirementContract::where('requirement_id', $request->requirement_id)
                ->whereHas('requirement', function($query) use ($user) {
                    $query->where('school_id', $user->school_id);
                })
                ->first();

            if ($existingContract) {
                // Contract exists, only create a new version
                return $this->createContractVersion($request, $existingContract, $user);
            }

            // No existing contract, create new contract with initial version
            // Prepare contract data with defaults and validation
            $contractData = $request->only([
                'requirement_id',
                'legal_first_name',
                'legal_last_name',
                'phone',
                'email',
                'job_title',
                'address',
                'client_name',
                'has_purchase_order',
                'purchase_order_ref',
                'status',
            ]);

            // Set default values for required fields
            $contractData['status'] = $contractData['status'] ?? 'pending_approval';
            $contractData['has_purchase_order'] = $contractData['has_purchase_order'] ?? false;
            // Add polymorphic audit fields using authenticated user
            $contractData['created_by_id']   = $user->id;
            $contractData['created_by_type'] = get_class($user);
            $contractData['updated_by_id']   = $user->id;
            $contractData['updated_by_type'] = get_class($user);
            // Create contract first
            $contract = SchoolRequirementContract::create($contractData);

            // Handle document upload after contract creation (outside transaction for S3)
            $fileUrl = null;
            if ($request->hasFile('document')) {
                try {
                    $fileUrl = $this->handleFileUpload($request, $contract, $user);
                } catch (Exception $uploadException) {
                    // If upload fails, delete the contract and rollback
                    $contract->delete();
                    DB::rollBack();
                    return ApiResponse::error($uploadException->getMessage(), 500);
                }
            }

            // Prepare version data
            $versionData = $request->only(['version_number', 'notes']);
            // Set defaults and required fields
            $versionData['school_requirement_contract_id'] = $contract->id;
            $versionData['file_url'] = $fileUrl;
            $versionData['version_number'] = $versionData['version_number'] ?? 'v1.0';
            $versionData['created_by_id']   = $user->id;
            $versionData['created_by_type'] = get_class($user);
            $versionData['updated_by_id']   = $user->id;
            $versionData['updated_by_type'] = get_class($user);

            // Create version record
            $version = SchoolRequirementContractVersion::create($versionData);

            DB::commit();
            // Load relationships for response
            $contract->load(['requirement.school:id,school_name', 'requirement.subject.subjectArea', 'requirement.classType', 'createdBy', 'updatedBy', 'versions']);
            return ApiResponse::success([
                'contract' => $contract,
                'version' => $version,
                'message' => 'Contract created successfully',
                'has_document' => !is_null($fileUrl)
            ], "Contract created successfully");
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error creating contract: ' . $e->getMessage(), ['user_id' => $user->id ?? 'unknown', 'request_data' => $request->except(['document']), 'trace' => $e->getTraceAsString()]);
            return ApiResponse::error('Failed to create contract. Please try again later.', 500);
        }
    }

    /**
     * Create a new version for existing contract
     */
     private function createContractVersion(Request $request, SchoolRequirementContract $contract, $user)
    {
        try {
            // Handle document upload first (before DB transaction)
            $fileUrl = $this->handleFileUpload($request, $contract, $user);

            // Get next version number
            $lastVersion = $contract->versions()->orderBy('created_at', 'desc')->first();
            $nextVersionNumber = $request->input('version_number');

            if (!$nextVersionNumber) {
                if ($lastVersion) {
                    // Extract version number and increment
                    $versionParts = explode('.', str_replace('v', '', $lastVersion->version_number));
                    $majorVersion = intval($versionParts[0] ?? 1);
                    $minorVersion = intval($versionParts[1] ?? 0);
                    $nextVersionNumber = 'v' . $majorVersion . '.' . ($minorVersion + 1);
                } else {
                    $nextVersionNumber = 'v1.0';
                }
            }

            // Create new version
            $versionData = [
                'school_requirement_contract_id' => $contract->id,
                'file_url' => $fileUrl,
                'version_number' => $nextVersionNumber,
                'notes' => $request->input('notes', 'New contract version'),
                'created_by_id' => $user->id,
                'created_by_type' => get_class($user),
                'updated_by_id' => $user->id,
                'updated_by_type' => get_class($user),
            ];

            $version = SchoolRequirementContractVersion::create($versionData);

            DB::commit();

            // Load relationships for response
            $contract->load(['requirement.school:id,school_name', 'requirement.subject.subjectArea', 'requirement.classType', 'createdBy', 'updatedBy', 'versions']);
            return ApiResponse::success([
                'contract' => $contract,
                'version' => $version,
                'message' => 'Contract version created successfully',
                'has_document' => !is_null($fileUrl)
            ], "Contract version created successfully");

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error creating contract version: ' . $e->getMessage(), [
                'contract_id' => $contract->id,
                'user_id' => $user->id,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Re-throw to be handled by calling method
        }
    }

    /**
     * Handle file upload for contract documents
     */
    private function handleFileUpload(Request $request, SchoolRequirementContract $contract, $user)
    {
        $fileUrl = null;
        if ($request->hasFile('document')) {
            $file = $request->file('document');

            // Validate file
            if (!$file->isValid()) {
                throw new Exception('Invalid file upload');
            }

            try {
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $sanitizedName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $originalName);
                $name = time() . '-' . $sanitizedName . '.' . $extension;
                $filename = 'uploads/school/contracts/' . $name;

                // Upload to S3 - this should happen before DB operations
                $uploadResult = uploads3image($filename, $file);

                if ($uploadResult) {
                    $fileUrl = $filename;
                    Log::info('Document uploaded successfully', [
                        'filename' => $filename,
                        'original_name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'contract_id' => $contract->id
                    ]);
                } else {
                    throw new Exception('Failed to upload file to S3');
                }
            } catch (Exception $uploadException) {
                Log::error('File upload failed: ' . $uploadException->getMessage(), [
                    'contract_id' => $contract->id,
                    'user_id' => $user->id,
                    'filename' => $filename ?? 'unknown'
                ]);
                throw new Exception('File upload failed. Please try again.');
            }
        }
        return $fileUrl;
    }

    /**
     * Update a specific requirement contract for a school.
     *
     * PUT /api/schools/requirements/contracts/{id}
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Delete a specific requirement contract for a school.
     *
     * DELETE /api/schools/requirements/contracts/{id}
     */
    public function destroy($id)
    {
        //
    }
}
