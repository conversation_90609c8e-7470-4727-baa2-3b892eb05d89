<?php

use App\AdditionalCertificateCategory;
use App\Http\Controllers\AdditionalCertificate;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Admin\CancelSubRequestController;
use App\Http\Controllers\Admin\InstructorAssignController;
use App\Http\Controllers\Admin\MakeupClassController;
use App\Http\Controllers\Admin\ProgramClassController;
use App\Http\Controllers\Admin\MailController;
use App\Http\Controllers\Admin\ManagePaymentsController;
use App\Http\Controllers\Admin\ManageReimbursementsController;
use App\Http\Controllers\Admin\ProgramCertificateController;
use App\Http\Controllers\Admin\ProgramController;
use App\Http\Controllers\Admin\RosterController;
use App\Http\Controllers\Admin\SchoolProgramController;
use App\Http\Controllers\Admin\ManageReviewsController;
use App\Http\Controllers\Admin\ApplicationController;
use App\Http\Controllers\Admin\FeedbackController;
use App\Http\Controllers\Admin\InstructorController;
use App\Http\Controllers\Admin\ZoomController;
use App\Http\Controllers\Admin\InvitationController;
use App\Http\Controllers\Admin\ProgramInviteController as AdminProgramInvite;
use App\Http\Controllers\Admin\ProgramRequestController;
use App\Http\Controllers\Admin\{ProgramViewClassController,ClassesController, InstituteController, ManageAdditionalSubjectApprovalController as AdminManageAdditionalSubjectApprovalController, ManageBudgetContentController, ManageEmailScheduleController, ManageMarketplaceProgramsController, ManageNewEducatorController as AdminManageNewEducatorController};
use App\Http\Controllers\Admin\ManageNewInstructorController;
use App\Http\Controllers\Admin\ManageEmailController;
use App\Http\Controllers\Admin\CredentialingAgencyController;

use App\Http\Controllers\WEB\ProgramInviteController;
use App\Http\Controllers\WEB\SchoolProgramInviteController;
use App\Http\Controllers\WEB\ProgramDetailsController;
use App\Http\Controllers\DependentController;
use App\Http\Controllers\WEB\AvailabilityController;
use App\Http\Controllers\WEB\ProgramPaymentsController;
use App\Http\Controllers\WEB\SchoolController;
use App\Http\Controllers\WEB\SchoolProgramDetailsController;
use App\Http\Controllers\WEB\SchoolPaymentController;
use App\Http\Controllers\CKeditorimguploadController;
use App\Http\Controllers\HeatMapController;
use App\Http\Controllers\NewInstructorController;

// QUICKBOOKS
use App\Http\Controllers\QuickBooksAuthController;
// SCHOOL
use App\Http\Controllers\School\SchoolController2;
use School\ClassBudgetController;

// MARKETPLACE
use MarketPlace\InstructorOnboardingController;
use App\Http\Controllers\MarketPlace\UserProfileController;
use Admin\ManageNewEducatorController;
use Admin\ManageAdditionalSubjectApprovalController;
use App\Http\Controllers\MarketPlace\ManageMarketplaceRolesController;
use App\Http\Controllers\MarketPlace\OpportunitiesController as MarketPlaceOpportunitiesController;
use MarketPlace\OpportunitiesController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
//Home
Route::get('/error', function () {
    return view('error.page');
})->name('error.page');

Route::get("/", "WEB\HomeController@index")->name("Home");
Route::get("/k12connections", "WEB\HomeController@k12userLogin")->name("k12userLogin");
Route::get("/sign-in", "WEB\AuthController@login")->name("Login");
Route::get("/signup", "WEB\AuthController@signup")->name("Signup");
Route::get("/teachers", "WEB\AuthController@signup")->name("teachers-Signup");
Route::get("/auth/google", "GoogleController@redirectToGoogle")->name("auth.google");
Route::get("auth/google/callback", "GoogleController@handleGoogleCallback")->name("handleGoogleCallback");
Route::get("auth/facebook", "FacebookController@facebookRedirect")->name("facebookRedirect");
Route::get("auth/facebook/callback", "FacebookController@loginWithFacebook")->name("loginWithFacebook");
Route::post("/forgot-password-frm", "WEB\AuthController@forgot_password");
Route::get("/reset-password/{id}", "WEB\AuthController@reset_passwords");
Route::post("/reset-password-frm", "WEB\AuthController@reset_password");
Route::get("/forgotpassword", "WEB\AuthController@forgotpassword")->name("forgotpassword");
Route::post("loginwithemail", "WEB\AuthController@loginwithemail");
Route::get("/sign-in-with-email", "WEB\AuthController@login")->name("signinemail");
Route::get("/sign-up-with-email", "WEB\AuthController@signup")->name("signupemail");
Route::post("create_user", "WEB\AuthController@createUser");
Route::get("email-link/{id}", "WEB\AuthController@emaillink");
Route::get("password-link/{id}", "WEB\AuthController@passwordlink");
Route::post("verify_email", "WEB\AuthController@verify_email");
Route::get("terms-condition", "HomeController@termscondition");
Route::get("privacy-policy", "HomeController@privacypolicy");
Route::post('upload-image', [CKeditorimguploadController::class, 'upload'])->name('upload.image');
//Home
    Route::get("v1/subject/{id}", "Admin\SubjectController@v1Subjects")->name("v1Subjects");
    Route::get("v1/subject-areas", "Admin\SubjectController@v1SubjectAreas")->name("v1SubjectAreas");


//instructor

Route::group(["middleware" => ["CheckWebSession"]], function () {
    Route::get("dependent/getSubSubjects/{id}", [DependentController::class, 'getSubSubjects'])->name("dependent.getSubSubjects");
    Route::post("submitFirstStep", "WEB\AuthController@submitFirstStep");
    Route::post("submitSecondStep", "WEB\AuthController@submitSecondStep");
    Route::post("submitThirdStep", "WEB\AuthController@submitThirdStep");
    Route::post("submitThirdStepTec", "WEB\AuthController@submitThirdStepTec");
    Route::post("submitFourthStep", "WEB\AuthController@submitFourthStep");
    Route::post("submitFiveStep", "WEB\AuthController@addAssessments");
    Route::post("filter_subject", "WEB\AuthController@filter_subject");
    Route::post("/submit_intro", "WEB\AuthController@submit_intro");
    Route::post("/submit_teaching", "WEB\AuthController@submit_teaching");
    Route::post("/submit_classrorm", "WEB\AuthController@submit_classrorm");
    Route::post("/submit_quiz", "WEB\AuthController@submit_quiz");
    Route::post("/save_quiz", "WEB\AuthController@save_quiz");
    Route::post("/subject_get", "WEB\AuthController@subject_get");
    Route::post("submitSecondStepOnBoarding","WEB\AuthController@submitSecondStepOnBoarding");
    Route::get("submit", "WEB\AuthController@submit");
    Route::post("verify_user", "WEB\AuthController@verify_user");
    Route::post("submitContract", "WEB\AuthController@submitContract");
    Route::get("interview-slot", "WEB\AuthController@interviewslot");
    Route::post("savetimeslot", "WEB\AuthController@savetimeslot");
    Route::post("save-reference", "WEB\AuthController@savereference");
    Route::get("logout", "WEB\AuthController@logout");
    Route::get("teacher-faq", "WEB\HomeController@faq");
    Route::post("/sendMsgstoSupport", "WEB\AuthController@sendMsg")->name("sendMsgstoSupport");
    Route::get("/get_user", "WEB\HomeController@get_user")->name("get_user");
    Route::get("/resource-detail/{id}", "WEB\HomeController@resourceDetails")->name("resourceDetails");
    Route::get("/resources", "WEB\HomeController@resources")->name("webresources");
    Route::get("/verify", "WEB\AuthController@verify")->name("verify");
    Route::get("/pendingverify", "WEB\AuthController@pendingverify")->name("pendingverify");
    Route::post("/get_subsubject", "WEB\AuthController@get_subsubject")->name("get_subsubject");
    Route::post("/get_subsubject_teaching", "WEB\AuthController@get_subsubject_teaching")->name("get_subsubject_teaching");
    Route::post("/get_subject_details", "WEB\AuthController@get_subject_details")->name("get_subject_details");
    Route::get("/onboarding-step/{id}", "WEB\AuthController@onboarding")->name("onboarding");
    Route::get("/new-onboarding-step/{id}", "WEB\AuthController@newonboarding")->name("newonboarding");
    Route::get("/onboarding-re-step/{id}","WEB\AuthController@reonboarding")->name("reonboarding");
    Route::get("/onboarding-details","WEB\DashboardController@onboardingdetails")->name("onboardingdetails");
    Route::get("/reference-details/{id}","WEB\AuthController@referencedetails")->name("referencedetails");
    Route::post("notification","WEB\DashboardController@notification")->name("notification");
    Route::get("web-dashboard", "WEB\DashboardController@index");
    Route::get("edit-profile", "WEB\AuthController@viewProfile");
    Route::post("create_completion", "WEB\DashboardController@createCompletion");
    Route::post("show_preview", "WEB\DashboardController@showPreview");
    Route::get('user/program-invites-list', [ProgramInviteController::class, 'getListData'])->name('user.program-invites.list');
    Route::post('user/program-invites/{id}/change-status', [ProgramInviteController::class, 'changeStatus'])->name('user.program-invites.change-status');
    Route::post('user/new-program-alert/{program}/decline', [ProgramInviteController::class, 'declineNewAlert'])->name('user.new-program-alert.decline');
    Route::get('user/program-calendar-tab-data', [ProgramInviteController::class, 'getcalendarTabData'])->name('user.program.get-calendar-tab-data');
    Route::get('user/program-tab-data', [ProgramInviteController::class, 'getTabData'])->name('user.program.get-tab-data');
    Route::get('user/apply-for-program/{id}', [ProgramInviteController::class, 'getApplyForm'])->name('user.program.get-apply-form');
    Route::post('user/apply-for-program/{id}', [ProgramInviteController::class, 'storeApplyForm'])->name('user.program.store-apply-form');
    Route::post('user/program-invites/request-replacement/{id}', [ProgramInviteController::class, 'storeReplacement'])->name('user.program-invites.request-replacement');
    Route::get('user/program-invites/request-main/{id}', [ProgramInviteController::class, 'getMainRequestForm'])->name('user.program-invites.request-main');
    Route::post('user/program-invites/request-main-replacement/{id}', [ProgramInviteController::class, 'storeMainReplacement'])->name('user.program-invites.request-main-replacement');
    Route::get('user/program-invites/request-sub/{program}', [ProgramInviteController::class, 'getSubRequestForm'])->name('user.program-invites.request-sub');
    Route::post('user/program-invites/request-sub/{program}', [ProgramInviteController::class, 'storeSubReplacement'])->name('user.program-invites.request-sub.store');
    Route::post('user/program-invites/cancel-sub/{program}', [ProgramInviteController::class, 'cancelSub'])->name('user.program-invites.cancel-sub');
    Route::get("/user/get-availability-fields", [AvailabilityController::class, 'getFields']);
    Route::get("my-availability", [AvailabilityController::class, 'myavailability']);
    Route::post("submitAvailability", [AvailabilityController::class, 'submitAvailability']);
    Route::post("submitAdministrative", "WEB\ProgramController@submitAdministrative");
    Route::get("notifications-settings", "WEB\ProgramController@notificationssettings");
    Route::post("get_location", "WEB\ProgramController@getLocation");
    Route::get("my-program", "WEB\ProgramController@index");
    Route::get("program-detail/{encryptedId}",  [ProgramDetailsController::class, 'index'])->name('user.program-detail');
    Route::get("inviteprogram-detail/{encryptedId}",  [ProgramDetailsController::class, 'inviteprogramdetails'])->name('user.inviteprogram-detail');
    Route::get("program-list/{program}",  [ProgramDetailsController::class, 'list'])->name('user.program-list');
    Route::get('user/completed-classes/{program}', [ProgramDetailsController::class, 'getCompletedClasses'])->name('user.completed-classes');
    Route::get("program-list-notes/{program}",  [ProgramDetailsController::class, 'getNotesForm'])->name('user.program-list.notes');
    Route::post("program-list-notes/{program}",  [ProgramDetailsController::class, 'storeNotesForm'])->name('user.program-list.notes.store');
    Route::get("program-list-edit-notes/{programNote}",  [ProgramDetailsController::class, 'editNotesForm'])->name('user.program-list.edit-notes');
    Route::post("program-list-edit-notes/{programNote}",  [ProgramDetailsController::class, 'updateNotesForm'])->name('user.program-list.notes.update');
    Route::get("program-list-edit-attendance-notes/{programNote}",  [ProgramDetailsController::class, 'editNotesattendanceForm'])->name('user.program-list.edit-attendance-notes');
    Route::post("program-list-edit-attendance-notes/{programNote}",  [ProgramDetailsController::class, 'updateNotesattendanceForm'])->name('user.program-list.attendance-notes.update');
    Route::post("program-list-cancel-notes/{programNote}",  [ProgramDetailsController::class, 'cancelNotesForm'])->name('user.program-list.notes.cancel');
    Route::get("program/get-district-schools/{id}",  [ProgramDetailsController::class, 'getSchoolBydistrict'])->name('program.get-district-schools');
    Route::get("program-class-notes/add-student{program}",  [ProgramDetailsController::class, 'addStudent'])->name('user.program-list.add-student');
    Route::get("user-payment-amounts/list",  [ProgramPaymentsController::class, 'list'])->name('user.payment-amounts.list');
    Route::get("user-payment-history/list",  [ProgramPaymentsController::class, 'paymentHistoryList'])->name('user.payment-history.list');
    Route::get('user/payment-tab-data', [ProgramPaymentsController::class, 'getTabData'])->name('user.payment.get-tab-data');
    Route::post('user/store-reimbursement-request', [ProgramPaymentsController::class, 'storeReimbursementRequest'])->name('store-reimbursement-request');
    Route::get('user/reimbursement-request-list', [ProgramPaymentsController::class, 'reimbursementsReqList'])->name('user.payment.reimbursement-requests.list');
    Route::get('user/reimbursement-list', [ProgramPaymentsController::class, 'reimbursementsList'])->name('user.payment.reimbursement.list');
    Route::get('user/payment-export', [ProgramPaymentsController::class, 'exportPayments'])->name('user.payment.export');
    Route::get('user/payment/view/{id}', [ProgramPaymentsController::class, 'viewPayment'])->name('user.payment.view');
    Route::get('user/reimbursement/view/{id}', [ProgramPaymentsController::class, 'viewReimbursement'])->name('user.reimbursement.view');
    Route::get('user/reimbursement-export', [ProgramPaymentsController::class, 'exportReimbursements'])->name('user.reimbursement.export');
    Route::get("new-program-alerts","WEB\ProgramController@newprogramalerts");
    Route::get("teaching-preferences","WEB\ProgramController@teachingpreferences");
    Route::get("payments", "WEB\ProgramController@payments");
    Route::get("administrative-information","WEB\ProgramController@administrativeinformation");
    Route::get("education-experience","WEB\DashboardController@educationExperience");
    Route::get("profile-information","WEB\ProgramController@profileinformation");
    Route::post("saveprofile","WEB\ProgramController@saveprofile");
    Route::get("account-settings","WEB\ProgramController@passwordmanagement");
    Route::post("update-password","WEB\ProgramController@updatePassword")->name('update-password');
    Route::post("update-account-status","WEB\ProgramController@updateAccountStatus")->name('update-account.status');
    Route::post("delete-account","WEB\ProgramController@deleteAccount")->name('delete-account');
    Route::get("contract", "WEB\ProgramController@contract");
    Route::get("status", "WEB\ProgramController@status");
    Route::get("messages", "WEB\ProgramController@message");
    Route::get("notifications", "WEB\NotificationController@index");
    Route::get("faq", "WEB\ProgramController@faq");
    Route::post("save_training", "WEB\DashboardController@saveTraining");
    Route::post("deletenotification", "WEB\NotificationController@deletenotification");
    Route::post("readnotification", "WEB\NotificationController@readnotification");
    Route::post("unreadnotification", "WEB\NotificationController@unreadnotification");
    Route::get("program-list-notice",  [ProgramDetailsController::class, 'getNoticeForm'])->name('user.program-list-notice.notice');
    Route::post("program-list-edit-notice",  [ProgramDetailsController::class, 'updateNoticeForm'])->name('user.program-list.notice.noticeupdate');
    Route::get("student-list/{program}/{programNote}",  [ProgramDetailsController::class, 'studentlist'])->name('user.student-list');
    Route::get('user/programDetails-tab-data', [ProgramDetailsController::class, 'getTabData'])->name('user.programDetails.get-tab-data');
    Route::get('user/program-review', [ProgramDetailsController::class, 'getFeedbackData'])->name('user.program-review');
    Route::get('user/program-roster', [ProgramDetailsController::class, 'getRosterData'])->name('user.program-roster');
    Route::get("user-instructor-reveiw/{encryptedprogramidId}",  [ProgramDetailsController::class, 'getReveiwForm'])->name('user.instructor-reveiw.reveiw');
    Route::post("user-instructor-add-reveiw",  [ProgramDetailsController::class, 'addprogramfeedback'])->name('user.instructor-reveiw.addreveiw');
    Route::get('user/program-invites/view-classes/{program}', [ProgramInviteController::class, 'viewclasses'])->name('user.program-invites.view-classes');
    Route::get('user/program-invites/makupsub-view-classes/{program}', [ProgramInviteController::class, 'makupsubviewclasses'])->name('user.program-invites.makupsub-view-classes');
    Route::get('user/program-invites/view-main-classes/{program}', [ProgramInviteController::class, 'viewMainclasses'])->name('user.program-invites.view-main-classes');
    Route::get('user/program-invites/view-automain-classes/{program}', [ProgramInviteController::class, 'viewautoMainclasses'])->name('user.program-invites.view-automain-classes');
    Route::get('user/program-invites/view-main-user-classes/{program}', [ProgramInviteController::class, 'viewMainUserclasses'])->name('user.program-invites.view-main-user-classes');
    Route::get('user/program-invites/view-invite-main-user-classes/{program}', [ProgramInviteController::class, 'viewinviteMainUserclasses'])->name('user.program-invites.view-invite-main-user-classes');
    // #W334 - 2. Instructors end should have program timezone displayed for the invite
    Route::get('user/program-invites/view-deadline/{program}', [ProgramInviteController::class, 'viewDeadlineWithTimezone'])->name('user.program-invites.view-deadline');
    //end
});

//instructor

//School
Route::get("/reset-platform-school-password/{user_id}", "WEB\AuthController@resetPlatformSchoolPassword")->name("reset-platform-school-password");
Route::post("/reset-platform-school-password-frm", "WEB\AuthController@resetPlatformSchoolPasswordFrm")->name("reset-platform-school-password-frm");
Route::post("loginwithemailschool", "WEB\AuthController@loginwithemailschool");
Route::get("/loginWithToken/{id}", "WEB\AuthController@loginWithToken");
Route::get("/school-forgotpassword", "WEB\AuthController@schoolforgotpassword")->name("schoolforgotpassword");
Route::post("/forgot-password-frm-school", "WEB\AuthController@forgot_password_school");
Route::get("/reset-password-school/{id}", "WEB\AuthController@reset_passwords_school");
Route::post("/reset-password-frm-school", "WEB\AuthController@reset_password_school");

Route::group(["middleware" => ["CheckSchoolSession"]], function () {
    // Route::get("/school-dashboard", "WEB\SchoolController@index")->name("indexschool");
    Route::get("/school-resources", "WEB\HomeController@school_resources")->name("school-resources");
    Route::get("/school-faq", "WEB\HomeController@school_faq")->name("school_faq");
    Route::get("school-notifications", "WEB\SchoolNotificationController@index");
    Route::post("school-deletenotification", "WEB\SchoolNotificationController@deletenotification");
    Route::post("school-readnotification", "WEB\SchoolNotificationController@readnotification");
    Route::post("school-unreadnotification", "WEB\SchoolNotificationController@unreadnotification");
    Route::get("school-logout", "WEB\SchoolController@logout");
    Route::get("search-instructor", "WEB\SchoolController@search_instructor");
    Route::get("new-school-program-alerts", "WEB\SchoolController@newprogram");
    Route::get("rate-instructor", "WEB\SchoolController@rate_instructor");
    Route::get("school-payments", "WEB\SchoolPaymentController@index");
    Route::get("school-logistics", "WEB\SchoolPaymentController@logistics");
    Route::get("school-profile-information", "WEB\SchoolController@school_profile_information");
    Route::get("school-notifications-settings", "WEB\SchoolController@school_notifications_settings");
    Route::get("school-messages", "WEB\SchoolController@messages");
    Route::get("acoount-setting", "WEB\SchoolController@acoountsetting");
    Route::get("update-contact-details", "WEB\SchoolController@update_contact_details");
    Route::post("saveschoolprofile","WEB\SchoolController@saveschoolprofile");
    Route::post("updatecontactdeatils","WEB\SchoolController@updatecontactdeatils");
    Route::post("schoolnotification","WEB\SchoolController@notification")->name("schoolnotification");
    Route::post("school-update-account-status","WEB\SchoolController@schoolupdateAccountStatus")->name('school-update-account.status');
    Route::post("school-update-password","WEB\SchoolController@updatePassword")->name('school-update-password');
    Route::post("school-delete-account","WEB\SchoolController@deleteAccount")->name('school-delete-account');
    Route::get('school/program-invites-list', [SchoolProgramInviteController::class, 'getListData'])->name('school.program-invites.list');
    Route::post('school/program-invites/{id}/change-status', [SchoolProgramInviteController::class, 'changeStatus'])->name('school.program-invites.change-status');
    Route::get('school/program-calendar-tab-data', [SchoolProgramInviteController::class, 'getcalendarTabData'])->name('school.program.get-calendar-tab-data');
    Route::get('school/program-tab-data', [SchoolProgramInviteController::class, 'getTabData'])->name('school.program.get-tab-data');
    Route::get('school/apply-for-program/{id}', [SchoolProgramInviteController::class, 'getApplyForm'])->name('school.program.get-apply-form');
    Route::post('school/apply-for-program/{id}', [SchoolProgramInviteController::class, 'storeApplyForm'])->name('school.program.store-apply-form');
    Route::post('school/program-invites/request-replacement/{id}', [SchoolProgramInviteController::class, 'storeReplacement'])->name('school.program-invites.request-replacement');
    Route::get("school-program-list-notice",  [SchoolController::class, 'getSchoolNoticeForm'])->name('school.program-list-notice.notice');
    Route::post("school-program-list-edit-notice",  [SchoolController::class, 'updateSchoolNoticeForm'])->name('school.program-list.notice.noticeupdate');
    Route::get("school-program-detail/{encryptedId}",  [SchoolProgramDetailsController::class, 'index'])->name('school.program-detail');
    Route::get("school-program-list/{program}",  [SchoolProgramDetailsController::class, 'list'])->name('school.program-list');
    Route::get("sub-details/{encryptedSubId}",  [SchoolProgramDetailsController::class, 'assignuserdetails'])->name('school.sub-details.details');
    Route::get("main-instructor-details/{encryptedMainId}/{encryptedprogramId}",  [SchoolProgramDetailsController::class, 'assignuserdetails'])->name('school.main-details.details');
    Route::get("school-payment-amounts/list",  [SchoolPaymentController::class, 'list'])->name('school.payment-amounts.list');
    Route::get('school/payment-export', [SchoolPaymentController::class, 'exportPayments'])->name('school.payment.export');
    Route::get("school-logistics-amounts/list",  [SchoolPaymentController::class, 'logisticslist'])->name('school.logistics-amounts.list');
    Route::get("school-instructor-reveiw/{encryptedStrId}/{encryptedprogramidId}",  [SchoolController::class, 'getReveiwForm'])->name('school.instructor-reveiw.reveiw');
    Route::post("school-instructor-add-reveiwins",  [SchoolController::class, 'addreveiwins'])->name('school.instructor-reveiw.addreveiwins');
    Route::get("school-instructor-rating/list",  [SchoolController::class, 'ratinglist'])->name('school.instructor-rating.list');
    Route::get("school-backgroundrequirements-checkstatus/{encryptedStrId}",  [SchoolProgramDetailsController::class, 'bgcheckstatus'])->name('school.program-backgroundrequirements.checkstatus');
    Route::get("school-medicalrequirements-checkstatus/{encryptedStrId}",  [SchoolProgramDetailsController::class, 'medicalcheckstatus'])->name('school.program-medicalrequirements.checkstatus');
    Route::get('school/programDetails-tab-data', [SchoolProgramDetailsController::class, 'getTabData'])->name('school.programDetails.get-tab-data');
    Route::get('school/program-reviews', [SchoolProgramDetailsController::class, 'getReviewData'])->name('school.program-reviews');
    Route::get('school/program-roster', [SchoolProgramDetailsController::class, 'getRosterData'])->name('school.program-roster');
    Route::get("school-instructor-reveiws/{encryptedprogramidId}",  [SchoolProgramDetailsController::class, 'getReveiwForm'])->name('school.instructor-reveiw.reveiws');
    Route::get('school/program-feedback', [SchoolProgramDetailsController::class, 'getFeedbackData'])->name('school.program-feedback');
    Route::get("school-program-feedback/{encryptedprogramidId}",  [SchoolProgramDetailsController::class, 'getfeedbackForm'])->name('school.program-reveiw.feedback');
    Route::post("school-program-add-reveiw",  [SchoolProgramDetailsController::class, 'addprogramfeedback'])->name('school.program-reveiw.addprogramfeedback');
    Route::get("school-program-list-programnotice/{program_id}",  [SchoolProgramDetailsController::class, 'getSchoolNoticeForm'])->name('school.program-list-notice.programnotice');
    Route::post("school-program-list-edit-programnotice",  [SchoolProgramDetailsController::class, 'updateSchoolNoticeForm'])->name('school.program-list.notice.programnoticeupdate');

    Route::get('post-requirements/{id?}', [SchoolController2::class, 'postRequirements'])->name('post-requirements');
    Route::get('school/tab-data', [SchoolController2::class, 'newSchoolGetTabData'])->name('new-school.get-tab-data');
    Route::get('school/budget/{id}', [SchoolController2::class, 'subjectBudget'])->name('new-school.subject-budget');
    Route::post('store-post-requirements', [SchoolController2::class, 'storePostRequirement'])->name('new-school.storePostRequirement');
    Route::get('list-requirements', [SchoolController2::class, 'listRequirements'])->name('new-school.listRequirements');
    Route::get('/get-chat-token', function () {
                return response()->json(['chatToken' => session('chatToken')]);
            });
    Route::post('update-post-requirements', [SchoolController2::class, 'updatePostRequirement'])->name('new-school.updatePostRequirement');
    Route::get('delete-post-requirements',[SchoolController2::class,'deletePostRequirement'])->name('deletePostRequirement');
    Route::match(['get', 'post'],'list-applicants/{encryptedId}',[SchoolController2::class,'listApplicants'])->name('new-school.list-applicants');
    Route::get('invite-talent/{encryptedId}',[SchoolController2::class,'inviteTalent'])->name('new-school.inviteTalent');
    Route::get('view-instructor-profile/{encryptedUserId}/{encryptedDataId}',[SchoolController2::class,'viewInstructor'])->name('new-school.view-instructor');
    Route::post('invite-instructor',[SchoolController2::class,'inviteInstructor'])->name('new-school.invite-instructor');
    Route::post('save-instructor',[SchoolController2::class,'saveInstructor'])->name('new-school.save-instructor');
    Route::post('message-instructor',[SchoolController2::class,'messageInstructor'])->name('new-school.message-instructor');
    Route::post('hire-instructor',[SchoolController2::class,'hireInstructor'])->name('new-school.hire-instructor');
    Route::get('view-hires/{encryptedId}/{screen?}',[SchoolController2::class,'viewHires'])->name('new-school.view-hires');
    Route::post('sendMsg',[SchoolController2::class,'sendMsgForSchool'])->name('sendMsg');
    Route::post('shortlist-instructor',[SchoolController2::class,'shortlistInstructor'])->name('new-school.shortlist-instructor');
    Route::get('chat-messages/{encryptedId}',[SchoolController2::class,'showChats'])->name('new-school.show-chats');
    Route::get('all-hires',[SchoolController2::class,'allHires'])->name('new-school.all-hires');
    Route::get('fetch-requirement/{refranceId}',[SchoolController2::class,'getNameByRefranceId']);
    Route::get('invite-talent2/{encryptedId}',[SchoolController2::class,'inviteTalent2'])->name('new-school.inviteTalent2');
    Route::post("get_subsubjects", [SchoolController2::class, "get_subsubjects"])->name("get_subsubjects");
    Route::get('requirement-details/{encryptedId}',[SchoolController2::class,'requirementDetails'])->name('new-school.requirementDetails');
    Route::get('filtered-applicants/{encryptedId}', [SchoolController2::class, 'sortAndFilterApplicants'])->name('new-school.sort');
    Route::get("get_certificates/{name}", [SchoolController2::class, "get_certificates"])->name('get_certificates');
    Route::post("duplicate-requirement-data/{encryptedId}", [SchoolController2::class, "duplicateRequirement"]);
    Route::post("close-requirement", [SchoolController2::class, "closeRequirement"]);
    Route::get("public_profile/{encryptedId}", [SchoolController2::class, "publicProfile"]);
    Route::get('list-hires/{encryptedId}', [SchoolController2::class, 'hiresTab'])->name('new-school.list-hires');
    Route::get("get_subject/{name}", [SchoolController2::class, "get_subject"])->name('get_subject');
    Route::get('search-filter/{encryptedId}',[SchoolController2::class, 'searchFilter'])->name('search-filter');
    Route::get('get_chatMsg/{encryptedId}',[SchoolController2::class, 'get_chatMsg'])->name('get_chatMsg');
    Route::post('invite-user', [SchoolController2::class, 'inviteUser'])->name('invite-user');
    Route::post('invite-bulk-user', [SchoolController2::class, 'inviteBulkUser'])->name('invite-bulkuser');
    Route::post('withdraw-invitation', [SchoolController2::class, 'withdrawInvitation'])->name('withdraw-invitation');
    Route::get('view-contract/{encryptedId}/{userId}',[SchoolController2::class, 'viewContractDetails'])->name('view-contract');
    Route::get('view-contract-tab/{encryptedId}',[SchoolController2::class, 'viewContractDetailsTab'])->name('view-contract-tab');
    Route::get('view-datatable/{encryptedId}/{userId}',[SchoolController2::class, 'getDatatable'])->name('view-datatable');
    Route::get('view-contract-overview-tab/{encryptedId}',[SchoolController2::class, 'viewContractOverviewTab'])->name('view-contract-overview-tab');
    Route::get('view-hire-offer/{encryptedId}',[SchoolController2::class, 'viewHireOffer'])->name('view_hire_offer');
    Route::post('store-hire-offer',[SchoolController2::class, 'storeHireOffer'])->name('store_hire_offer');
    Route::get('hire-search-filter',[SchoolController2::class, 'searchHire'])->name('search-hire');
    Route::post('update-hire-offer/{encryptedId}',[SchoolController2::class, 'updateHireOffer'])->name('updateHireOffer');
    Route::post('store-class-setup/{encryptedId}',[SchoolController2::class, 'storeClassSetup'])->name('storeClassSetup');
    Route::post('save-class-setup/{encryptedId}',[SchoolController2::class, 'saveClassSetup'])->name('saveClassSetup');
    Route::get('get-classes',[SchoolController2::class, 'getClasses']);
    Route::get('/export-datatable/{encryptedId}/{userId}/{type}', [SchoolController2::class, 'exportDatatable'])->name('export.datatable');
    Route::get('roster-sample', [SchoolController2::class, 'exportRosterSample'])->name('new-school.roster-sample');
    Route::get('view-roster', [SchoolController2::class, 'viewRoster'])->name('new-school.view-roster');
    Route::get('get-applicant-lists', [SchoolController2::class, 'getLists'])->name('new-school.get-applicant-lists');
    Route::post('/save-applicant-list', [SchoolController2::class, 'bookmarkListSave'])->name('new-school.bookmarkListSave');
    Route::get('get_chatData/{encryptedId}',[SchoolController2::class, 'get_chat_by_user'])->name('get_chatData');
    Route::get('all-requirements',[SchoolController2::class, 'allRequirements'])->name('allRequirements');
    Route::get('contracts',[SchoolController2::class, 'allContracts'])->name('allContracts');
    Route::post('remove-note-document', [SchoolController2::class, 'removeNoteDocument'])->name('remove-note-document');
    Route::get('discover', [SchoolController2::class, 'discover'])->name('new-school.discover');
    Route::get('reports', [SchoolController2::class, 'reports'])->name('new-school.reports');
    Route::get('classes-calendar', [SchoolController2::class, 'classCalendar'])->name('new-school.classCalendar');
    Route::get('reports/transaction-history', [SchoolController2::class, 'transactionHistoryReports'])->name('new-school.transaction-history');
    Route::get('/reports/get-contracts/{instructorId}', [SchoolController2::class, 'getContracts']);
    Route::get('/all-classes', [SchoolController2::class, 'allClasses'])->name('new-school.allClasses');
    Route::get('/view-classes', [SchoolController2::class, 'viewClasses'])->name('new-school.viewClasses');
    Route::get('/download_report_by_contract', [SchoolController2::class, 'export_report_by_contract'])->name("export_spend_by_contracts");
    Route::get('/download_report_by_educator', [SchoolController2::class, 'export_spend_by_educator'])->name("export_spend_by_educator");
    Route::post("/get_dynamic_school_data",[SchoolController::class,"get_dynamic_school_data"])->name("get_dynamic_school_data");
    Route::get("/profile",[SchoolController2::class,"profile_details"])->name("profile_details");
    Route::get("/messages-screen",[SchoolController2::class,"messages"])->name("new-school.messages");
    Route::get('/school-timezone', [SchoolController2::class, 'getSchoolTimezone']);
    Route::post("/create_category",[SchoolController2::class,"create_category"])->name('create_category');
    Route::post("/getallcateogry",[SchoolController2::class,"getallcateogry"])->name('getallcateogry');
    Route::post("/instructor_category_save",[SchoolController2::class,"instructor_category_save"])->name('instructor_category_save');
    Route::post("/notfication_read",[SchoolController2::class,"notfication_read"])->name('notfication_read');
    // Route::get("calculate_budget",[SchoolController2::class,"calculate_budget"])->name('calculate_budget');
    Route::resource('calculate-budgets', ClassBudgetController::class)->except(['show']);
    // routes/web.php or routes/api.php (depending on your setup)

    Route::post('calculate-budgets/custom-calculate', [\App\Http\Controllers\School\ClassBudgetController::class, 'calculateBudget'])->name('calculate-budgets.calculate');
    Route::post('calculate-budgets/search_budget', [\App\Http\Controllers\School\ClassBudgetController::class, 'search_budget'])->name('search_budget');
    Route::post('calculate-budgets/edit_budget', [\App\Http\Controllers\School\ClassBudgetController::class, 'edit_budget'])->name('edit_budget');
    Route::post('calculate-budgets/duplicate_budget',[\App\Http\Controllers\School\ClassBudgetController::class, 'duplicate_budget'])->name("duplicate_budget");
    Route::post("calculate-budgets/get_all_state",[\App\Http\Controllers\School\ClassBudgetController::class, 'get_all_state'])->name("get_all_state");
    Route::post("calculate-budgets/sort_budget",[\App\Http\Controllers\School\ClassBudgetController::class, 'sort_budget'])->name("sort_budget");

});

//School

//Admin
Route::match(["GET", "POST"], "/admin", "LoginController@index")->middleware("AdminUnAuth");
Route::get("admin-dashboard", "UserController@index");
Route::post("store-featured-teachers", "UserController@storeFeaturedTeachers")->name('admin.store.featured.teachers');
Route::post("refresh-featured-teachers", "UserController@refreshFeaturedTeachers")->name('admin.refresh-featured-teachers');
Route::any("login", "LoginController@index");
Route::get("forgot-password", "LoginController@forgot_password");
Route::post("admin-forgot-password", "LoginController@admin_forgot_password");
Route::post("admin-otp-match", "LoginController@admin_otp_match");
Route::post("admin-reset-password", "LoginController@admin_reset_password");
Route::post("loginpost", "LoginController@loginpost");

// QUICKBOOK
Route::get('quickbooks/connect', [QuickBooksAuthController::class, 'connect'])->name('quickbook.connect');
Route::get('callback', [QuickBooksAuthController::class, 'callback']);
Route::get('accounts', [QuickBooksAuthController::class, 'account']);
Route::get('contractors', [QuickBooksAuthController::class, 'createContractor']);

Route::group(["middleware" => ["CheckSession"]], function () {
    Route::get("admin-profile", "UserController@profile_details");
    Route::get("edit-admin-profile", "UserController@edit_profile_details");
    Route::get("change-admin-password", "UserController@change_password");
    Route::get("admin-signout", "LoginController@signout");
    Route::post("updateChangePassword", "UserController@updateChangePassword");
    Route::post("personal_info", "UserController@personalInfo");
    Route::post("personal_contact", "UserController@personalContact");
    Route::post("edit-contact", "UserController@contactUpdate");
    Route::post("update_profile_image", "UserController@imageupload");
    Route::post("show_note_history","Admin\ApplicationController@showNoteHistory");
    Route::post("show_filter","Admin\ApplicationController@show_filter");
    Route::post("show_filter_ins","Admin\ApplicationController@show_filter_ins");
    Route::post("submitFilter","Admin\ApplicationController@submitFilter");
    Route::post("submitFilterins","Admin\ApplicationController@submitFilterins");
    Route::post("show_status_model","Admin\ApplicationController@showStatusModel");
    Route::resource("terms-setting", "Admin\SettingTermsController");
    Route::get("terms-setting-list","Admin\SettingTermsController@index")->name("terms-setting-list");
    Route::post("terms-setting-changes","Admin\SettingTermsController@statuschange");
    Route::get("viewtermssetting/{id}","Admin\SettingTermsController@viewtermssetting")->name("viewtermssetting");
    Route::post("edittermssetting","Admin\SettingTermsController@update")->name("edittermssetting");
    Route::get("terms-and-conditions","Admin\SettingTermsController@termsandcondition");
    Route::resource("faqs", "Admin\FaqsController");
    Route::get("faqs-list", "Admin\FaqsController@index");
    Route::get("addfaqs", "Admin\FaqsController@create")->name("addfaqs");
    Route::post("storefaqs", "Admin\FaqsController@store")->name("storefaqs");
    Route::post("change-status-faqs", "Admin\FaqsController@statuschange");
    Route::post("delete-faqs", "Admin\FaqsController@deletefaqs");
    Route::get("viewfaqs/{id}", "Admin\FaqsController@viewfaqs")->name("viewfaqs");
    Route::post("editfaqs", "Admin\FaqsController@update")->name("editfaqs");
    Route::get("add-admin-management", "Admin\AdminManagementController@index");
    Route::get("add-admin", "Admin\AdminManagementController@add_admin");
    Route::post("save-admin", "Admin\AdminManagementController@save_admin");
    Route::post("admin-status-changes","Admin\AdminManagementController@status_change");
    Route::post("delete-admin", "Admin\AdminManagementController@delete_admin");
    Route::get("edit-admin/{id}", "Admin\AdminManagementController@edit_admin");
    Route::post("admin-update", "Admin\AdminManagementController@admin_update");
    Route::post("adminimagechange","Admin\AdminManagementController@adminimagechange");
    Route::get("manage-role", "Admin\RoleController@index");
    Route::get("add-role", "Admin\RoleController@addrole");
    Route::post("role-save", "Admin\RoleController@save_role");
    Route::get("edit-role/{id}", "Admin\RoleController@edit_role");
    Route::post("role-update", "Admin\RoleController@role_update");
    Route::post("delete-role", "Admin\RoleController@roledelete");
    Route::post("role-update-status", "Admin\RoleController@status_change");
    Route::get("add-permission/{id}", "Admin\PermissionController@index");
    Route::post("add_permission", "Admin\PermissionController@add_permission");
    Route::get("manage-district", "Admin\DistrictController@index")->name("district");
    Route::get("adddistrict", "Admin\DistrictController@adddistrict")->name("adddistrict");
    Route::post("save-district", "Admin\DistrictController@savedistrict");
    Route::post("change-status-district","Admin\DistrictController@status_change");
    Route::post("delete-district", "Admin\DistrictController@delete");
    Route::get("edit-district/{id}", "Admin\DistrictController@edit");
    Route::post("update-district", "Admin\DistrictController@update");
    Route::get("manage-cbo", "Admin\CBOController@index")->name("cbo");
    Route::get("addcbo", "Admin\CBOController@addcbo")->name("adddcbo");
    Route::post("save-cbo", "Admin\CBOController@savecbo");
    Route::post("change-status-cbo", "Admin\CBOController@status_change");
    Route::post("delete-cbo", "Admin\CBOController@delete");
    Route::get("edit-cbo/{id}", "Admin\CBOController@edit");
    Route::post("update-cbo", "Admin\CBOController@update");
    Route::get("add-school", "Admin\InstituteController@index")->name("addnewinstitute");
    Route::get("school-list","Admin\InstituteController@listnewinstitute")->name("listnewinstitute");
    Route::post("storeinstitute", "Admin\InstituteController@store");
    Route::post("change-status-institute","Admin\InstituteController@statuschange");
    Route::post("deleteinstitute","Admin\InstituteController@deleteinstitute")->name("deleteinstitute");
    Route::get("viewschool/{id}","Admin\InstituteController@viewinstitute")->name("viewinstitute");
    Route::post("editschool", "Admin\InstituteController@update")->name("editinstitute");
    Route::get("viewschooldetails/{id}","Admin\InstituteController@viewinstitutedetails")->name("viewinstitutedetails");
    Route::get("change-school-password/{id}", "Admin\InstituteController@change_password");
    Route::get("viewschoolprogram/{id}","Admin\InstituteController@viewschoolprogram")->name("viewschoolprogram");
    Route::post("updateChangePasswordforschool", "Admin\InstituteController@updateChangePassword" );
    Route::get("viewdetails/{id}","Admin\AdminManagementController@viewdetails")->name("viewdetails");
    Route::get("change-staff-password/{id}","Admin\AdminManagementController@change_password");
    Route::post("updateChangePasswordforstaff","Admin\AdminManagementController@updateChangePassword");
    Route::get("instructor-list", "Admin\InstructorController@index")->name("instructor");
    Route::get("instructor/export", "Admin\InstructorController@export")->name("instructor.export");
    Route::get("viewinstructordetails/{id}/{id1}","Admin\InstructorController@viewinstructordetails")->name("viewinstructordetails");
    Route::get("edit-instructor/{id}","Admin\InstructorController@editinstructor")->name("editinstructor");
    Route::post("instructor-update","Admin\InstructorController@instructorupdate")->name("instructor-update");
    Route::get("add-instructor","Admin\InstructorController@add_instructor")->name("add_instructor");
    Route::get("sub-instructor-list","Admin\InstructorController@subinstructorlist")->name("subinstructorlist");
    Route::get("listrecruiter", "Admin\RecruiterController@index")->name("recruiter");
    Route::get("edit-recruiter/{id}","Admin\AdminManagementController@edit_admin");
    Route::get("new-application-list","Admin\ApplicationController@index")->name("newapplication");
    Route::get("Under-Reveiw-application-list","Admin\ApplicationController@Reveiw")->name("Underapplication");
    Route::get("Resubmit-request-application-list","Admin\ApplicationController@Resubmit")->name("Resubmitapplication");
    Route::get("Pending-Interview-application-list","Admin\ApplicationController@PendingInterview")->name("Pendingapplication");
    Route::get("Approved-application-list","Admin\ApplicationController@Approved")->name("Approvedapplication");
    Route::get("Active-application-list","Admin\ApplicationController@Activeapplication")->name("Activeapplication");
    Route::get("Reject-application-list","Admin\ApplicationController@Rejectapplication")->name("Rejectapplication");
    Route::get("All-application-list","Admin\ApplicationController@allapplication")->name("Allapplication");
    Route::get("view-application/{id}/{id2}","Admin\ApplicationController@viewapplication")->name("viewapplication");
    Route::post("change-status-profile","Admin\ApplicationController@changestatus")->name("changeviewapplication");
    Route::post("save-Interview","Admin\ApplicationController@addInterview")->name("addInterview");
    Route::post("save_rubric", "Admin\ApplicationController@save_rubric")->name("save rubric");
    Route::post("saveassub", "Admin\ApplicationController@saveassub")->name("saveassub");
    Route::post("delete-interview", "Admin\ApplicationController@deleteinterview");
    Route::post("cancel-interview", "Admin\ApplicationController@cancelinterview");
    Route::post("request-form", "Admin\ApplicationController@requestform");
    Route::post("request-program-form", "Admin\ProgramController@requestProgramform");
    Route::get("class-list", "Admin\ClassController@index")->name("class");
    Route::get("add-class", "Admin\ClassController@addclass")->name("addclass");
    Route::post("save-class", "Admin\ClassController@saveclass");
    Route::post("change-status-class", "Admin\ClassController@status_change");
    Route::post("delete-class", "Admin\ClassController@delete");
    Route::get("edit-class/{id}", "Admin\ClassController@edit");
    Route::post("update-class", "Admin\ClassController@update");
    Route::get("subject-list", "Admin\SubjectController@index")->name("subject");
    // Testimonials
    Route::get("testimonial-list", "Admin\TestimonialController@index")->name("testimonial");
    Route::post("testimonial-store", "Admin\TestimonialController@store")->name("testimonial.store");
    Route::post("testimonial-delete", "Admin\TestimonialController@destroyAjax")->name("testimonial.delete");
    Route::get("add-sub-subject/{id}", "Admin\SubjectController@subIndex");
    Route::post("upload-sub-subject-image", "Admin\SubjectController@uploadImage")->name('subsubject.upload.image');
    Route::get("add-subject", "Admin\SubjectController@add")->name("addsubject");
    Route::post("submit-sub-subject", "Admin\SubjectController@submitsubsubject");
    Route::post("save-subject", "Admin\SubjectController@save");
    Route::post("change-status-subject","Admin\SubjectController@status_change");
    Route::post("delete-subject", "Admin\SubjectController@delete");
    Route::get("edit-subject/{id}", "Admin\SubjectController@edit");
    Route::post("update-subject", "Admin\SubjectController@update");
    Route::get("programs/calendar", "Admin\ProgramCalendarController@index")->name("admin.program.calendar");
    Route::get("programs/{id}", "Admin\ProgramController@index")->name("program");
    Route::post("programs/export", "Admin\ProgramController@export")->name("program.export");
    Route::get("publish-program","Admin\ProgramController@publishprogram")->name("publish program");
    Route::get("add-program", "Admin\ProgramController@add")->name("addprogram");
    Route::post("save-program", "Admin\ProgramController@save")->name("save");
    Route::get("add-new-program", "Admin\ProgramController@add")->name("addnewprogram");
    Route::get("add-school-new-program/{id}", "Admin\ProgramController@schoolprogramadd")->name("schoolprogramadd");
    Route::get("edit-program/{id}", "Admin\ProgramController@edit")->name("editprogram");
    Route::post("update-program", "Admin\ProgramController@update")->name("updateprogram");
    Route::get("check-status", "Admin\ProgramController@checkStatus")->name("checkstatus");
    Route::post("change-status-program","Admin\ProgramController@status_change_program");
    Route::get("view-program/{id}/{id2}", "Admin\ProgramController@viewprogram")->name("viewprogram");
    Route::post("post_invite_program", "Admin\ProgramController@post_invite_program");
    Route::post("get_invite_instructors","Admin\ProgramController@get_invite_instructors");
    Route::get("getSchoolBydistrict/{id}","Admin\ProgramController@getSchoolBydistrict");
    Route::delete("delete-program/{program}","Admin\ProgramController@delete")->name("admin.program.delete");
    Route::post("sendcred", "Admin\InstituteController@sendcred");
    Route::post("change_status_model","Admin\ApplicationController@change_status_model");
    Route::post("change_status", "Admin\ApplicationController@statusChange");
    Route::get("manage-training-video", "Admin\TrainingController@index")->name("training");
    Route::get("addtrainingvideo", "Admin\TrainingController@add")->name("addtraining");
    Route::post("save-training", "Admin\TrainingController@save");
    Route::post("change-status-training","Admin\TrainingController@status_change");
    Route::post("delete-training", "Admin\TrainingController@delete");
    Route::get("edit-training/{id}", "Admin\TrainingController@edit");
    Route::post("update-training", "Admin\TrainingController@update");
    Route::get("manage-form", "Admin\FormController@index");
    Route::get("add-form", "Admin\FormController@create");
    Route::post("save-form", "Admin\FormController@store");
    Route::post("change-status-form","Admin\FormController@status_change");
    Route::post("delete-form", "Admin\FormController@destroy");
    Route::get("edit-form/{id}", "Admin\FormController@edit");
    Route::post("update-form", "Admin\FormController@update");
    Route::post("get_assign_model","Admin\ProgramController@get_assign_model");
    Route::post("save-assign","Admin\ProgramController@save_assign");
    Route::post("getratemodel", "Admin\ApplicationController@getratemodel");
    Route::post("update_payrate", "Admin\ApplicationController@update_payrate");
    Route::get("manage-resources", "Admin\ResourcesController@index")->name("resources");
    Route::get("add-resource", "Admin\ResourcesController@add")->name("add-resource");
    Route::post("save-resources", "Admin\ResourcesController@save");
    Route::post("change-status-resources","Admin\ResourcesController@status_change");
    Route::post("delete-resources", "Admin\ResourcesController@delete");
    Route::get("edit-resource/{id}", "Admin\ResourcesController@edit");
    Route::post("update-resources", "Admin\ResourcesController@update");
    Route::post("removerow", "Admin\SubjectController@removerow");
    Route::get("instructor-chat/{id}", "Admin\InstructorController@chat")->name("instructor-chat");
    Route::post( "deletereview","Admin\ManageReviewsController@deletereview")->name("deletereview");
    Route::get("adminchat", "Admin\ChatController@index")->name("chat");
    Route::post("get_assign_app_model","Admin\ApplicationController@get_assign_app_model");
    Route::post("save-app-assign","Admin\ApplicationController@save_assign");
    Route::get("get_user_admin","Admin\ApplicationController@get_user_admin");
    Route::post("sendMsgForAdmin", "Admin\ChatController@sendMsgForAdmin");
    Route::post("sendMultiMsgForAdmin", "Admin\ChatController@sendMultiMsgForAdmin");
    Route::get("view-mail/{id}","Admin\MailController@index");
    Route::get("notice-list","Admin\NoticeController@index");
    Route::get("no-permission","Admin\PermissionController@nopermission");
    Route::post("search_invite_ins_program","Admin\InstructorAssignController@search_invite_ins_program");
    Route::get("instructor-invite-list", "Admin\InstructorAssignController@instructorinvitelist")->name( "instructorinvitelist");
    Route::get("instructor-sub-invite-list", "Admin\InstructorAssignController@instructorsubinvitelist")->name("instructorsubinvitelist");
    Route::get("attendance-list", "Admin\ProgramClassController@attendancelist")->name("attendance-list");
    Route::get("all-notifications","Admin\NotificationController@index");
    Route::post("delete-notification","Admin\NotificationController@destroy");
    Route::get("createMeeting","Admin\ZoomController@createMeeting");
    Route::get('/create-sub-account', 'Admin\ZoomController@createSubAccount');
    Route::get('/getAcoountId', 'Admin\ZoomController@getAcoountId');
    Route::get('/createCandidates', 'Admin\CheckrController@createCandidates');
    Route::get("addnotification", "Admin\NotificationController@create")->name(
        "addnotification"
    );

    Route::get('list-school-management-setting',[InstituteController::class,'schoolManagementSetting'])->name('schoolManagementSetting');
    Route::get('add-school-management-setting',[InstituteController::class,'addSchoolManagementSetting'])->name('addSchoolManagementSetting');
    Route::post('store-school-management-setting',[InstituteController::class,'storeSchoolManagementSetting'])->name('storeSchoolManagementSetting');
    Route::get('delete-school-management-setting',[InstituteController::class,'deleteSchoolManagementSetting'])->name('deleteSchoolManagementSetting');
    Route::get('requirements-list',[InstituteController::class,'listPostRequirements'])->name('listPostRequirements');

    Route::get('credentialing-agency', [CredentialingAgencyController::class, 'listAgency'])->name('agency');
    Route::get('add-agency', [CredentialingAgencyController::class, 'CreateAgency'])->name('AddAgency');
    Route::post('store-agency', [CredentialingAgencyController::class, 'storeAgency'])->name('StoreAgency');
    Route::get('edit-agency/{id}', [CredentialingAgencyController::class, 'editAgency'])->name('EditAgency');
    Route::post('update-agency/{id}', [CredentialingAgencyController::class, 'updateAgency'])->name('UpdateAgency');
    Route::post('delete-agency', [CredentialingAgencyController::class, 'DeleteAgency'])->name('DeleteAgency');
    Route::get('add-certificates/{id}', [CredentialingAgencyController::class, 'AddCertificates'])->name('AddCertificates');
    Route::post('store-certificates', [CredentialingAgencyController::class, 'StoreCertificates'])->name('StoreCertificates');
    Route::post('delete-certificate', [CredentialingAgencyController::class, 'deleteCertificates'])->name('deleteCertificates');

    Route::get('/additional_certificate', [AdditionalCertificate::class, 'index'])->name("additional_certificate");
    Route::get("add_additional_category", [AdditionalCertificate::class, 'add_additional_category'])->name("add_additional_category");
    Route::post("/storeCategory",[AdditionalCertificate::class,"storeCategory"])->name('storeCategory');
    Route::get("/add_sub_category/{id}",[AdditionalCertificate::class,"add_sub_category"])->name('add_sub_category');
    Route::post("/storesubcategory",[AdditionalCertificate::class,"storesubcategory"])->name('storesubcategory');
    Route::get("/delete_existing_sub_Category",[AdditionalCertificate::class,"delete_existing_sub_Category"])->name("delete_existing_sub_Category");
    Route::get("/edit_category_view/{id}",[AdditionalCertificate::class,"edit_category_view"])->name("edit_category_view");
    Route::post("/updatecategory",[AdditionalCertificate::class,"updatecategory"])->name("updatecategory");
    Route::get("/delete_existing_category",[AdditionalCertificate::class,"delete_existing_category"])->name("delete_existing_category");

    Route::post("delete-payment-admin", "Admin\ManagePaymentsController@delete_payment");
    Route::group(['prefix' => 'admin', 'as' => 'admin.'], function () {
        Route::get('manage-payments', [ManagePaymentsController::class, 'index'])->name('manage-payments');
        Route::get('get-program-details', [ManagePaymentsController::class, 'getProgramDetails'])->name('manage-payments.get-program-details');
        Route::get('manage-payments/{id}', [ManagePaymentsController::class, 'view'])->where('id', '[0-9]+')->name('manage-payments.view');
        Route::get('manage-payments-viewinstructorpaymentmodel/{id}', [ManagePaymentsController::class, 'viewinstructorpaymentmodel'])->where('id', '[0-9]+')->name('manage-payments.viewinstructorpaymentmodel');
        Route::get('manage-payments-confirmpaymentmodel', [ManagePaymentsController::class, 'confirmpaymentmodel'])->name('manage-payments.confirmpaymentmodel');
        Route::post('update-payment-status', [ManagePaymentsController::class, 'updatePaymentStatus'])->name('update-payment-status');
        Route::get('manage-payments-update/{id}', [ManagePaymentsController::class, 'update'])->where('id', '[0-9]+')->name('manage-payments-update.update');
        Route::post('manage-payments-update/{id}', [ManagePaymentsController::class, 'updatePayment'])->name('manage-payments-update.updatePayment');
        Route::post('add-payments',[ManagePaymentsController::class, 'addPayment'])->name('manage-payments.addPayment');
        Route::get('manage-payments/instructorPaymentsexport', [ManagePaymentsController::class, 'instructorPaymentsexport'])->name('manage-payments.instructorPaymentsexport');
        Route::get('manage-payments/export', [ManagePaymentsController::class, 'export'])->name('manage-payments.export');
        Route::get('manage-application/export', [ApplicationController::class, 'export'])->name('manage-application.export');
        Route::get('manage-payments/reimbursements', [ManageReimbursementsController::class, 'index'])->name('manage-payments.reimbursements');
        Route::get('manage-payments/instructorPayments', [ManagePaymentsController::class, 'instructorPayments'])->name('manage-payments.instructorPayments');
        Route::get('manage-payments-reimbursement/{id}', [ManageReimbursementsController::class, 'view'])->where('id', '[0-9]+')->name('manage-payments.view-reimbursement');
        Route::get('manage-payments/export-reimbursement', [ManageReimbursementsController::class, 'export'])->name('manage-payments.export-reimbursement');
        Route::post('manage-payments/reimbursements/update-status/{reimbursement}', [ManageReimbursementsController::class, 'updateStatus'])->name('manage-payments.update.reimbursement.status');
        Route::post('manage-payments/reimbursements/update-paid-status', [ManageReimbursementsController::class, 'updatePaidStatus'])->name('manage-payments.update.reimbursement.paid-status');
        Route::get('program/cancel-sub-requests', [CancelSubRequestController::class, 'index'])->name('program.cancel-sub-requests');
        Route::get('program/view-sub-classes/{id}', [CancelSubRequestController::class, 'view'])->name('program.view-sub-classes');
        Route::post('program/cancel-sub-request/{id}', [CancelSubRequestController::class, 'update'])->name('program.update.cancel-sub-request');
        Route::get('program/invites', [AdminProgramInvite::class, 'index'])->name('program.invites');
        Route::post('program/invites/export', [AdminProgramInvite::class, 'export'])->name('program.invite.export');
        Route::get('program/requests', [ProgramRequestController::class, 'index'])->name('program.requests');
        Route::get('program/allrequests', [ProgramRequestController::class, 'allrequests'])->name('program.allrequests');
        Route::get('program/feedbacks/{program_id}', [FeedbackController::class, 'index'])->name('program.feedbacks');
        Route::delete('program/feedback/{eid}', [FeedbackController::class, 'delete'])->name('program.feedback.delete');
        Route::get('program/invitationsList/{program_id}', [InvitationController::class, 'index'])->name('program.invitationsList');
        Route::delete('program/invitation/{eid}', [InvitationController::class, 'delete'])->name('program.invitation.delete');
        Route::get('program/add-invitation/{program}', [InvitationController::class, 'addinvitation'])->name('program.add-invitation');
        Route::post('program/add-invitation', [InvitationController::class, 'store'])->name('program.add-invitation.store');
        Route::get('program/resend-invitation/{id}', [InvitationController::class, 'resendinvitation'])->name('program.resend-invitation');
        Route::post('program/resend-invitation', [InvitationController::class, 'resendstore'])->name('program.resend-invitation.resendstore');
        Route::get('program/view-invite-classes/{id}', [ProgramRequestController::class, 'view'])->name('program.view-invite-classes');
        Route::get('program/view-ins-invite-classes/{id}', [AdminProgramInvite::class, 'viewclasses'])->name('program.view-ins-invite-classes');
        Route::post('program/update-request/{id}', [ProgramRequestController::class, 'updateRequest'])->name('program.update.request');
        Route::post('program/update-cancelinviterequest/{id}', [ProgramRequestController::class, 'cancelinviterequest'])->name('program.update.cancelinviterequest');
        Route::get('program/replacement-requests', [ProgramRequestController::class, 'replacementList'])->name('program.replacement-requests');
        Route::post('program/replacement-requests/export', [ProgramRequestController::class, 'export'])->name('program.replacement-requests.export');
        Route::get('program/{id}/replacement-form', [ProgramRequestController::class, 'getReplacementForm'])->name('program.replacement-form');
        Route::get('program/{id}/replacement-sub-form', [ProgramRequestController::class, 'getReplacementsubForm'])->name('program.replacement-sub-form');
        Route::post('program/{id}/replacement-form', [ProgramRequestController::class, 'storeReplacementForm'])->name('program.replacement-form.store');
        Route::get('program/class-notes/add-comment/{programNote}', [ProgramClassController::class, 'addComment'])->name('class-notes.add-comment');
        Route::post('program/class-notes/add-comment/{programNote}', [ProgramClassController::class, 'storeComment'])->name('class-notes.add-comment.store');
        Route::get('program/class-notes/add-meeting/{program}', [ProgramClassController::class, 'addMeeting'])->name('class-notes.add-meeting');
        Route::post('program/class-notes/add-meeting/{program}', [ProgramClassController::class, 'storeMeeting'])->name('class-notes.add-meeting.store');
        Route::post('program-admin-notes/store/{program}', [ProgramClassController::class, 'storeAdminNotes'])->name('program-admin-notes.store');
        Route::post('program-admin-notes/update/{program}', [ProgramClassController::class, 'updateAdminNotes'])->name('program-admin-notes.update');
        Route::delete('program-admin-notes/delete/{programAdminNote}', [ProgramClassController::class, 'deleteAdminNotes'])->name('program-admin-notes.delete');
        Route::post('program/class-notes/complete-class/{programNote}', [ProgramClassController::class, 'completeClass'])->name('class-notes.complete-class');
        Route::post('program/class-notes/holiday-class/{programNote}', [ProgramClassController::class, 'markAsHoliday'])->name('class-notes.holiday-class');
        Route::get('program/class-notes/cancel-class/{programNote}', [MakeupClassController::class, 'cancelClass'])->name('class-notes.cancel-class');
        Route::get('program/class-notes/assign-sub/{programNote}', [MakeupClassController::class, 'assignSub'])->name('class-notes.assign-sub');
        Route::post('program/class-notes/cancel-class/{programNote}', [MakeupClassController::class, 'storeCancelClass'])->name('class-notes.cancel-class.store');
        Route::get('program/create-makeup-class/{programNote}', [MakeupClassController::class, 'add'])->name('program.makeup-class.create');
        Route::post('program/store-makeup-class/{programNote}', [MakeupClassController::class, 'store'])->name('program.makeup-class.store');
        Route::get('program/show-mail-form/{eid}', [MailController::class, 'show_mail_form'])->name('program.show-mail-form');
        Route::post('program/submit-mail/{eid}', [MailController::class, 'mailstore'])->name('program.submit-mail.mailstore');
        Route::delete('program/maildelete/{eid}', [MailController::class, 'maildelete'])->name('program.maildelete');
        Route::get('program/show-mail-details/{eid}', [MailController::class, 'show_mail_details'])->name('program.show-mail-details');
        Route::get('program/view-classes/{eId}', [ProgramViewClassController::class, 'list'])->name('program.view-class.list');
        Route::get('program/edit-view-classes/{programNote}', [ProgramViewClassController::class, 'editViewClass'])->name('program.view-class.edit');
        Route::get('program/create-view-classes/{program}', [ProgramViewClassController::class, 'createViewClass'])->name('program.view-class.create');
        Route::post('program/create-view-classes/{program}', [ProgramViewClassController::class, 'storeViewClass'])->name('program.view-class.store');
        Route::put('program/edit-view-classes/{programNote}', [ProgramViewClassController::class, 'updateViewClass'])->name('program.view-class.update');
        Route::delete('program/delete-view-classes/{programNote}', [ProgramViewClassController::class, 'deleteViewClass'])->name('program.view-class.delete');
        Route::get('program/assign-main-instructor/{program}', [InstructorAssignController::class, 'assignMain'])->name('program.assign-main-instructor');
        Route::post('program/assign-main-instructor/{program}', [InstructorAssignController::class, 'storeMain'])->name('program.assign-main-instructor.store');
        Route::get('program/remove-instructor/{program}', [InstructorAssignController::class, 'removeMainInstructor'])->name('program.remove-instructor');
        Route::post('program/remove-instructor', [InstructorAssignController::class, 'removeMainInstructorStore'])->name('program.remove-instructor.store');
        Route::post('program/cancel-sub/{program}', [InstructorAssignController::class, 'cancelSub'])->name('program.cancel-sub');
        Route::get('program-assign-multi/main', [InstructorAssignController::class, 'assignMultiMain'])->name('program.assign-multi-main');
        Route::post('program-assign-multi/main', [InstructorAssignController::class, 'storeMultiCommon'])->name('program.assign-multi-main.store');
        Route::get('program/assign-sub-instructor/{program}', [InstructorAssignController::class, 'assignSub'])->name('program.assign-sub-instructor');
        Route::get('program/assign-substandby-instructor/{program}', [InstructorAssignController::class, 'assignSubstandby'])->name('program.assign-substandby-instructor');
        Route::post('program/assign-sub-instructor/{program}', [InstructorAssignController::class, 'storeSub'])->name('program.assign-sub-instructor.store');
        Route::get('program-assign-multi/sub', [InstructorAssignController::class, 'assignMultiSub'])->name('program.assign-multi-sub');
        Route::post('program-assign-multi/sub', [InstructorAssignController::class, 'storeMultiSub'])->name('program.assign-multi-sub.store');
        Route::post('program-assign-search/', [InstructorAssignController::class, 'searchInstructors'])->name('program.assign-search');
        Route::get('program-assign-multi/search', [InstructorAssignController::class, 'assignMultiSearch'])->name('program.assign-multi-search');
        Route::post('program-assign-multi/search', [InstructorAssignController::class, 'storeMultiSearch'])->name('program.assign-multi-search.store');
        Route::get('program-assign/get-instructors/{program}', [InstructorAssignController::class, 'getProgaramInstructors'])->name('program.get-instructors');
        Route::get('program/add-certificate', [ProgramCertificateController::class, 'add'])->name('program.add-certificate');
        Route::post('program/add-certificate/{program}', [ProgramCertificateController::class, 'store'])->name('program.add-certificate.store');
        Route::get('program-class/assign-sub-instructor/{programNote}', [InstructorAssignController::class, 'assignClassSub'])->name('program-class.assign-sub-instructor');
        Route::post('program-class/assign-sub-instructor/{programNote}', [InstructorAssignController::class, 'storeClassSub'])->name('program-class.assign-sub-instructor.store');
        Route::get('program-multi-class/assign-sub/{program}', [InstructorAssignController::class, 'assignMultiClassSub'])->name('program-multi-class.assign-sub');
        Route::post('program-multi-class/assign-sub/{program}', [InstructorAssignController::class, 'storeMultiClassSub'])->name('program-multi-class.assign-sub.store');
        Route::get('program/add-no-class-dates', [ProgramController::class, 'addNoClassDates'])->name('program.add-no-class-dates');
        Route::get('program/resend-invite/{id}', [InstructorAssignController::class, 'showResendInvite'])->name('program.resend-invite');
        Route::post('program/resend-invite/{id}', [InstructorAssignController::class, 'resendInvite'])->name('program.resend-invite.store');
        Route::delete('program/delete-stand-by/{id}', [InstructorAssignController::class, 'deleteStandBy'])->name('program.delete-stand-by');
        Route::get('program/add-roster-import/{program}', [RosterController::class, 'addRosterImport'])->name('program.add-roster-import');
        Route::get('program/add-roster/{program}', [RosterController::class, 'addRoster'])->name('program.add-roster');
        Route::post('program/add-roster/{program}', [RosterController::class, 'store'])->name('program.add-roster.store');
        Route::post('program/add-roster-importstore/{program}', [RosterController::class, 'importstore'])->name('program.add-roster-importstore.importstore');
        Route::delete('program/add-roster-deleterow/{roster}', [RosterController::class, 'deleteStudent'])->name('program.add-roster-deleterow.delete');
        Route::get('program/edit-roster/{roster}', [RosterController::class, 'editRoster'])->name('program.edit-roster');
        Route::get('program/class-notes/view-attendance/{programNote}', [ProgramClassController::class, 'viewattendance'])->name('class-notes.view-attendance');
        Route::get('school/programs/{school_id}', [SchoolProgramController::class, 'programlist'])->name('school.programs.list');
        Route::get('manage-reviews', [ManageReviewsController::class, 'index'])->name('manage-reviews');
        Route::get('manage-rating/{ins_id}', [ManageReviewsController::class, 'ins_rating'])->name('manage-rating');
        Route::get('review/edit/{reviewId}', [ManageReviewsController::class, 'edit'])->name('review.edit');
        Route::get('review/add/{user_id}', [ManageReviewsController::class, 'add'])->name('review.add');
        Route::post('review/update/{reviewId}', [ManageReviewsController::class, 'update'])->name('review.update');
        Route::post('review/save/{user_id}', [ManageReviewsController::class, 'save'])->name('review.save');
        Route::get('program/logistics/{program_id}', [SchoolProgramController::class, 'viewlogistics'])->name('program.logistics');
        Route::get('program/logisticslist/{program_id}', [SchoolProgramController::class, 'logisticslist'])->name('program.logisticslist');
        Route::get('program/addLogistics/{program_id}', [SchoolProgramController::class, 'addLogistics'])->name('program.addLogistics');
        Route::post('program/submit-Logistics/{program_id}', [SchoolProgramController::class, 'submitLogistics'])->name('program.submit-Logistics');
        Route::delete('logistics/delete/{logistic_id}', [SchoolProgramController::class, 'delete'])->name('logistics.delete');
        Route::get('logistics/edit-logistics/{logistic_id}', [SchoolProgramController::class, 'editLogistics'])->name('logistics.edit-logistics');
        Route::post('program/update-Logistics/{logistic_id}', [SchoolProgramController::class, 'updateLogistics'])->name('program.update-Logistics');
        Route::get('user/sendmsg', [InstructorController::class, 'sendinsmsg'])->name('user.sendmsg');
        Route::get('admin/program/zoommodel', [ProgramController::class, 'getzoommodel'])->name('program.getzoommodel');
        Route::post('admin/program/createzoomlink', [ZoomController::class, 'createMeeting'])->name('program.create-zoom-link');
        Route::get('admin/program/getrezoommodel/{program_id}', [ProgramController::class, 'getrezoommodel'])->name('program.getrezoommodel');
        Route::post('admin/program/editzoomlink', [ZoomController::class, 'editMeeting'])->name('program.edit-zoom-link');
        Route::get('program/assign-shortlist-marketplace-instructor/{program}', [InstructorAssignController::class, 'assignMarketplaceInstructor'])->name('program.assign-shortlist-marketplace-instructor');
        Route::post('program/shortlist-marketplace-instructor-store', [InstructorAssignController::class, 'shortlistMarketplaceInstructorStore'])->name('program.assign-marketplace-instructor.store');
        Route::get('marketplace-instructor-list', [InstructorAssignController::class, 'marketplaceInstructorList'])->name('marketplace-instructor-list');
        Route::delete('marketplace-instructor.delete/{id}', [InstructorAssignController::class, 'marketplaceInstructorListDelete'])->name('marketplace-instructor.delete');

        // Global Classes List
        Route::get('classes/list', [ClassesController::class, 'index'])->name('classes.list');
        Route::get('classes/makeup', [ClassesController::class, 'makeupCancel'])->name('classes.makeup');
        Route::post('classes/export', [ClassesController::class, 'export'])->name('classes.export');
        Route::post('classes/makeup/export', [ClassesController::class, 'cancelExport'])->name('cancelledClasses.export');

        Route::match(['get', 'post'], 'k12connections/set-instructor-id/{id?}', [ManageNewInstructorController::class, 'setInstructorId'])->name('set.instructor.id');
        Route::get("k12connections/application/{id}","Admin\ManageNewInstructorController@marketplaceviewapplication")->name("marketplaceviewapplication");
        Route::post('applicants-export', [ManageNewInstructorController::class, 'exportApplicants'])->name('marketplace-applicant-export');

        // new instructor
        Route::resource('k12connections/manage-educator', ManageNewEducatorController::class);
        Route::get('k12connections/manage-educator/{id}/availability', [AdminManageNewEducatorController::class, 'show'])->name('educator.availability');
        Route::get('k12connections/manage-educator/{id}/subjects', [AdminManageNewEducatorController::class, 'show'])->name('educator.subjects');
        Route::get('k12connections/manage-educator/{id}/phone', [AdminManageNewEducatorController::class, 'show'])->name('educator.phone');
        Route::get('k12connections/manage-educator/{id}/notification', [AdminManageNewEducatorController::class, 'show'])->name('educator.notification');
        Route::get('k12connections/manage-educator/{id}/locations', [AdminManageNewEducatorController::class, 'show'])->name('educator.locations');
        Route::post('k12connections/update-educator-substitute', [AdminManageNewEducatorController::class, 'updateEducatorSubstitue'])->name('update-educator-substitute');

        // Manage Subject Approval
        Route::get('k12connections/manage-subject-approval/{id?}', [AdminManageAdditionalSubjectApprovalController::class, 'index'])->name('manage-subject-approval');
        Route::get('k12connections/subject-approval-modal/{id}', [AdminManageAdditionalSubjectApprovalController::class, 'approveSubjectModelOpen'])->name('subject.approval.modal');
        Route::get('k12connections/reject-subject-modal/{id}', [AdminManageAdditionalSubjectApprovalController::class, 'rejectSubjectModelOpen'])->name('reject-subject.modal');
        Route::post('k12connections/subject-status-update', [AdminManageAdditionalSubjectApprovalController::class, 'updateSubjectStatus'])->name('subject.status.update');

        Route::get('k12connections/manage-instructor/{id}',[ManageNewInstructorController::class, 'manageInstructor'])->name('new-instructor.manage-instructor');
        Route::get('k12connections/add-applicant',[ManageNewInstructorController::class, 'addApplicant'])->name('new-instructor.add-applicant');
        Route::post('k12connections/store-applicant',[ManageNewInstructorController::class, 'storeApplicant'])->name('new-instructor.store-applicant');
        Route::post('k12connection/update-sustitute', [ManageNewInstructorController::class, 'updateSubstitue'])->name('new-instructor.update-sustitute');
        Route::get("k12connections/edit-onboarding-instructor/{id}",[ManageNewInstructorController::class, "editnewinstructor"])->name("editnewinstructor");
        Route::get("k12connections/sendMsg",[ManageNewInstructorController::class, "sendmsg"])->name("onboardinginstructor.sendmsg");
        Route::post("k12connections/sendOnboardingMsgForAdmin",[ManageNewInstructorController::class, "sendOnboardingMsgForAdmin"])->name("sendOnboardingMsgForAdmin");
        Route::post("k12connections/update_user_status",[ManageNewInstructorController::class, "update_user_status"])->name("update_user_status");
        Route::get("k12connections/viewonboardinginstructordetails/{id}/{id1}",[ManageNewInstructorController::class, "viewnewinstructordetails"])->name("viewnewinstructordetails");
        Route::post("k12connections/delete-instructor", [ManageNewInstructorController::class, "delete_admin"]);
        Route::get("k12connections/instructor-observation", [ManageNewInstructorController::class, "instructorObservation"])->name('instructor.observation');
        Route::post("k12connections/store-observation-note", [ManageNewInstructorController::class, "storeObservationNote"])->name('store.observation.note');
        Route::get("k12connections/sendRejectOnboardingMsg", [ManageNewInstructorController::class, "sendRejectOnboardingMsg"])->name('sendRejectOnboardingMsg');
        Route::get("k12connections/sendChangeRequestOnboardingMsg", [ManageNewInstructorController::class, "sendChangeRequestOnboardingMsg"])->name('sendChangeRequestOnboardingMsg');
        Route::get("k12connections/sendActiveOnboardingMsg", [ManageNewInstructorController::class, "sendActiveOnboardingMsg"])->name('sendActiveOnboardingMsg');
        Route::get("k12connections/instructorsSubjectBudget", [ManageNewInstructorController::class, "instructorsSubjectBudget"])->name('instructorsSubjectBudget');
        Route::get("k12connections/application-instructor-chat/{id}", [ManageNewInstructorController::class, "chat"])->name("onboarding-instructor-chat");
        Route::get("k12connections/programs/{id}", [ManageMarketplaceProgramsController::class, "programsList"])->name('marketplace-programsList');
        Route::get("k12connections/add-program", [ManageMarketplaceProgramsController::class, "addProgram"])->name('marketplace-addProgram');
        Route::post("k12connections/save-program", [ManageMarketplaceProgramsController::class, "saveProgram"])->name('marketplace-saveProgram');
        Route::get("k12connections/getrezoommodel/{program_id}", [ManageMarketplaceProgramsController::class, "getrezoommodel"])->name('marketplace-getrezoommodel');
        Route::get("k12connections/view-program/{id}/{id2}", [ManageMarketplaceProgramsController::class, "viewprogram"])->name('marketplace-viewprogram');

        Route::get("k12connections/requirements", [ManageMarketplaceProgramsController::class, "requirementsList"])->name('marketplace-requirements');
        Route::get("k12connections/requirements/invite", [ManageMarketplaceProgramsController::class, "requirementsInvite"])->name('marketplace-requirementsInvite');
        Route::get("k12connections/requirements/invite-search", [ManageMarketplaceProgramsController::class, "requirementsInviteSearch"])->name('marketplace-requirementsInviteSearch');
        Route::post("k12connections/requirements/send-invite", [ManageMarketplaceProgramsController::class, "sendInvite"])->name('marketplace-sendInvite');
        Route::post("k12connections/requirements/update-invite", [ManageMarketplaceProgramsController::class, "updateInviteStatus"])->name('marketplace-updateInviteStatus');
        Route::get("k12connections/view-requirements/{id}", [ManageMarketplaceProgramsController::class, "viewRequirements"])->name('marketplace-viewRequirements');
        Route::put("k12connections/requirements/contract-status/{id}", [ManageMarketplaceProgramsController::class, "updateStatus"])->name('marketplace-updateStatus');
        Route::post("k12connections/requirements/contract-version/{id}", [ManageMarketplaceProgramsController::class, "uploadVersion"])->name('marketplace-uploadVersion');
        Route::post("k12connections/requirements/update-contract-content", [ManageMarketplaceProgramsController::class, "updateContractContent"])->name('marketplace-updateContractContent');
        Route::post("k12connections/requirements/shortlist-applicant", [ManageMarketplaceProgramsController::class, "shortlistApplicant"])->name('marketplace-shortlistApplicant');
        Route::get("k12connections/add-requirements", [ManageMarketplaceProgramsController::class, "addRequirements"])->name('marketplace-addRequirements');
        Route::get("k12connections/calculate-budget/{id}", [ManageMarketplaceProgramsController::class, "calculatePlatformBudget"])->name('marketplace-calculateBudget');
        Route::get("k12connections/fetch-school-data", [ManageMarketplaceProgramsController::class, "fetchSchoolData"])->name('marketplace-fetchSchoolData');
        Route::post("k12connections/save-requirements", [ManageMarketplaceProgramsController::class, "saveRequirements"])->name('marketplace-saveRequirements');
        Route::get("k12connections/edit-requirements/{id}", [ManageMarketplaceProgramsController::class, "editRequirements"])->name('marketplace-editRequirements');
        Route::post("k12connections/update-requirements/{id}", [ManageMarketplaceProgramsController::class, "updateRequirements"])->name('marketplace-updateRequirements');
        Route::delete("k12connections/delete-requirements", [ManageMarketplaceProgramsController::class, "deleteRequirements"])->name('marketplace-deleteRequirements');
        Route::post("k12connections/duplicate-requirements", [ManageMarketplaceProgramsController::class, "duplicateRequirements"])->name('marketplace-duplicateRequirements');

        Route::get("k12connections/applied-requests", [ManageMarketplaceProgramsController::class, "showInviteHistory"])->name('marketplace-appliedRequests');
        Route::get("k12connections/program-invites", [ManageMarketplaceProgramsController::class, "showProgramInvites"])->name('marketplace-programInvites');
        Route::post("k12connections/applied-requests/update-status", [ManageMarketplaceProgramsController::class, "updateAppliedRequestStatus"])->name('marketplace-updateAppliedRequestStatus');

        Route::post('k12connections/update_user_background', [ManageMarketplaceProgramsController::class, "update_user_background"])->name('update_user_background');
        Route::get('heat-map', [HeatMapController::class, 'heatMap'])->name('heatMap');
        Route::get('heat-map-data/{schoolId}', [HeatMapController::class, 'heatMapData'])->name('heatMapData');
        Route::get('k12connections/manage-platform-schools', [ManageNewInstructorController::class, 'managePlatformSchools'])->name('manage-platform-schools');
        Route::get("k12connections/manage-Budget-schools",[ManageNewInstructorController::class,'manageBudgetSchools'])->name("manage-budget-school");
        Route::post("k12connections/update-Budget-schools",[ManageNewInstructorController::class,'updateBudgetSchools'])->name("update-budget-school");
        Route::get("k12connections/school-budget-by-subject-area/{subjectAreaId}",[ManageNewInstructorController::class,'schoolBudgetbySubjectArea'])->name("budget-by-subject-area");
        Route::get('k12connections/view-platform-schools/{id}', [ManageNewInstructorController::class, 'viewPlatformSchools'])->name('view-platform-schools');
        Route::get('k12connections/add-platform-schools', [ManageNewInstructorController::class, 'addPlatformSchools'])->name('add-platform-schools');
        Route::post('k12connections/save-platform-schools', [ManageNewInstructorController::class, 'savePlatformSchools'])->name('save-platform-schools');
        Route::get('k12connections/edit-platform-schools/{id}', [ManageNewInstructorController::class, 'editPlatformSchools'])->name('edit-platform-schools');
        Route::post('k12connections/update-platform-school/{id}', [ManageNewInstructorController::class, 'updatePlatformSchools'])->name('update-platform-schools');
        Route::post('k12connections/send-credentials-platform-schools', [ManageNewInstructorController::class, 'sendCredentialsPlatformSchools'])->name('sendCredentialsPlatformSchools');
        Route::post('k12connections/update-school-user-roles', [ManageNewInstructorController::class, 'updateSchoolUserRoles'])->name('marketplace-updateSchoolUserRoles');
        Route::post('k12connections/delete-platform-schools', [ManageNewInstructorController::class, 'deletePlatformSchools'])->name('delete-platform-schools');

        // Email Schedule Routes
        Route::resource('k12connections/manage-email-schedules', 'Admin\\ManageEmailScheduleController');

        // Manage New Resources
        Route::resource('k12connections/manage-resources', 'Admin\\ManageNewResourcesController');
        Route::post('k12connections/manage-resources/{id}/status', 'Admin\\ManageNewResourcesController@updateStatus')->name('manage-resources.status');

        Route::get('k12connections/manage-school-roles', [ManageNewInstructorController::class, 'listSchoolRoles'])->name('list-school-roles');
        Route::get('k12connections/manage-school-role/{id}', [ManageNewInstructorController::class, 'manageSchoolRoles'])->name('manage-school-roles');
        Route::post('k12connections/update-school-role/{id}', [ManageNewInstructorController::class, 'updateSchoolRoles'])->name('update-school-roles');
        Route::post('/get-timezone', [ManageNewInstructorController::class, 'getTimezone'])->name('get.timezone');

        Route::post('budget-content/import', [ManageBudgetContentController::class, 'importBudgetContent'])->name('import_budget_content');
        Route::get('budget-content/export/{stateId}', [ManageBudgetContentController::class, 'exportBudgetContent'])->name('export_budget_content');
        Route::get('budget-content-management', [ManageBudgetContentController::class, 'BudgetContentManagement'])->name('BudgetContentManagement');

        Route::match(['get', 'post'], 'budget-content-subject', [ManageBudgetContentController::class, 'BudgetContentSubject'])->name('BudgetContentSubject');
        // Route::match(['get', 'post'], 'budget_states', [ManageBudgetContentController::class, 'budget_states'])->name('budget_states');
        Route::post("/budget_states_update",[ManageBudgetContentController::class,"budget_states_update"])->name('budget_states_update');
        Route::get('budget-content/subjects/{subject_area_id}/{state_id}', [ManageBudgetContentController::class, 'getSubjectsBySubjectAreaId'])->name('getSubjectsBySubjectAreaId');
        Route::post("/budget_single_state_data",[ManageBudgetContentController::class,"budget_single_state_data"])->name("budget_single_state_data");
        Route::post("/syncToAllStates",[ManageBudgetContentController::class,"syncToAllStates"])->name('syncToAllStates');

        // Manage Emails
        Route::group(['prefix' => 'email-template', 'as' => 'email-template.'], function () {
            Route::get('/', [ManageEmailController::class, 'index'])->name('index');

            // Layout
            Route::get('/layout/{id}/edit', [ManageEmailController::class, 'editLayout'])->name('layout.edit');
            Route::post('/layout/{id}', [ManageEmailController::class, 'updateLayout'])->name('layout.update');

            // Template
            Route::get('/template/{id}/edit', [ManageEmailController::class, 'editTemplate'])->name('template.edit');
            Route::post('/template/{id}', [ManageEmailController::class, 'updateTemplate'])->name('template.update');
        });

        // Manage Marketplace Roles Routes
        Route::get('k12connections/manage-marketplace-roles', [ManageMarketplaceRolesController::class, 'index'])->name('manage-marketplace-roles');
        Route::get('k12connections/add-marketplace-role', [ManageMarketplaceRolesController::class, 'addRoles'])->name('add-marketplace-role');
        Route::post('k12connections/save-marketplace-role', [ManageMarketplaceRolesController::class, 'saveRoles'])->name('save-marketplace-role');
        Route::get('k12connections/edit-marketplace-role/{id}', [ManageMarketplaceRolesController::class, 'editRoles'])->name('edit-marketplace-role');
        Route::post('k12connections/update-marketplace-role', [ManageMarketplaceRolesController::class, 'updateRoles'])->name('update-marketplace-role');
        Route::post('k12connections/delete-marketplace-role', [ManageMarketplaceRolesController::class, 'deleteRoles'])->name('delete-marketplace-role');
        Route::get('k12connections/add-permission/{id}', [ManageMarketplaceRolesController::class, 'openPermissionPage'])->name('open-permission-page');
        Route::post('k12connections/add-permission', [ManageMarketplaceRolesController::class, 'addPermission'])->name('add-permission');
    });


});

// new instructor
    Route::get("k12connections/sign-in", [NewInstructorController::class, "instructorlogin"])->name("instructorLogin");
    Route::get("k12connections/sign-up", [NewInstructorController::class, "signupInstructor"])->name("instructor-Signup");
    Route::post("k12connections/create_instructor", [NewInstructorController::class, "createInstructor"]);
    //when user click on the email
    Route::get("k12connections/instructor-verify-email-link/{id}", [NewInstructorController::class, "verifyMarketplaceInstructor"]);
    Route::get("k12connections/instructor-email-link/{id}", [NewInstructorController::class, "instructoremaillink"]);
    Route::post("k12connections/verify_instructor_email", [NewInstructorController::class, "verify_instructor_email"]);
    Route::get("k12connections/forgotpassword", [NewInstructorController::class, "forgotpassword"])->name("forgotpassword");
    Route::post("k12connections/loginwithinstructoremail", [NewInstructorController::class, "loginwithinstructoremail"]);
    Route::post("/k12connections/forgot-instructor-password-frm", [NewInstructorController::class, "forgot_instructor_password"]);
    Route::get("/k12connections/reset-instructor-password/{id}", [NewInstructorController::class, "resetInstructorPasswords"]);
    Route::post("/k12connections/reset-instructor-password-frm", [NewInstructorController::class, "reset_instructor_password"]);
    Route::post("/k12connections/videouploads3", [NewInstructorController::class, "s3uploadlink"]);
    Route::post("/k12connections/videoRecordingUploads3", [NewInstructorController::class, "s3recordingUploadlink"]);
    Route::post('/k12connections/processing-video', [NewInstructorController::class, 'updateProcessingVideo']);

    Route::get('k12connections/reset-applicant-password/{user_id}', [ManageNewInstructorController::class, 'resetApplicantPassword'])->name('new-instructor.reset-applicant-password');
    Route::post('k12connections/reset-applicant-password-frm', [ManageNewInstructorController::class, 'resetApplicantPasswordFrm'])->name('new-instructor.reset-applicant-password-frm');
    // Route::get("/instructor-dashboard", [NewInstructorController::class, "instructorDashboard"]);
    Route::group(["middleware" => ["CheckInstructorSession"]], function () {
        Route::get("/k12connections/verify-instructor", [NewInstructorController::class, "verifyInstructor"]);
        Route::post("/k12connections/get_subsubjects", [NewInstructorController::class, "get_subsubjects"])->name("get_subsubjects");
        Route::get("/k12connections/application", [NewInstructorController::class, "newonboarding"])->name("newonboarding");
        Route::get("/k12connections/lession_planning", [NewInstructorController::class, "getLessonPlanning"])->name("getLessonPlanning");
        Route::get("/k12connections/tab-data/{tab}", [NewInstructorController::class, "newonboardingTabData"])->name("tab-data");
        Route::post("k12connections/submitFirstStepInstructor", [NewInstructorController::class, "submitFirstStepInstructor"]);
        Route::post("k12connections/submitSecondStepInstructor", [NewInstructorController::class, "submitSecondStepInstructor"]);
        Route::post("k12connections/submitThirdStepInstructor", [NewInstructorController::class, "submitThirdStepInstructor"]);
        Route::post("k12connections/submitFifthStepInstructor", [NewInstructorController::class, "submitFifthStepInstructor"]);
        Route::post('k12connections/remove-profile-video', [NewInstructorController::class, 'removeProfileVideo']);
        Route::post("/k12connections/submit_quiz_instructor", [NewInstructorController::class, "submit_quiz_instructor"]);
        Route::post("/k12connections/submit_instructor_agreement", [NewInstructorController::class, "submit_instructor_agreement"]);
        Route::apiResource('/k12connections/onboarding', InstructorOnboardingController::class)->except(['show']);
        Route::get("/k12connections/dashboard", [NewInstructorController::class, "instructorDashboard"])->name('instructorDashboard');
        Route::get("/k12connections/logout", [NewInstructorController::class, "logout"])->name('log');
        Route::get("/k12connections/get_certification/{id}", [NewInstructorController::class, "get_certification"])->name('get_certification');
        Route::get("/k12connections/get_certificates/{id}", [NewInstructorController::class, "get_certificates"])->name('get_certificates');
        Route::get("/k12connections/get_academics/{id}", [NewInstructorController::class, "get_academics"])->name('get_academics');
        Route::get("/k12connections/get_experience/{id}", [NewInstructorController::class, "get_experience"])->name('get_experience');
        Route::get("/k12connections/get_other_experience/{id}", [NewInstructorController::class, "get_other_experience"])->name('get_other_experience');
        Route::get("/k12connections/get_reference/{id}", [NewInstructorController::class, "get_reference"])->name('get_reference');
        Route::post("/k12connections/delete_onboarding_step/{id}", [NewInstructorController::class, "delete_onboarding_step"])->name('delete_onboarding_step');
        Route::get('k12connections/submit-profile', [NewInstructorController::class, "submitProfile"])->name('submit-profile');
        Route::post('delete-profile-image/{id}', [NewInstructorController::class, "deleteProfileImage"])->name('deleteProfileImg');
        Route::get('k12connections/contract-details', [NewInstructorController::class, "contractDetails"])->name('contract-details');
        Route::match(['get', 'post'],'k12connections/agreement-details', [NewInstructorController::class, "agreement"])->name('agreement-details');
        Route::post('k12connections/agreement-submit', [NewInstructorController::class, "submitAgreement"])->name('agreement-submit');
        Route::post('k12connections/both-agreement-submit', [NewInstructorController::class, "submitBothAgreement"])->name('both-agreement-submit');
        Route::post('k12connections/declined-contract', [NewInstructorController::class, "declinedContract"])->name('declined-contract');
        Route::get('k12connections/get-recording', [NewInstructorController::class, 'getRecording']);
        Route::post('k12connections/remove-assessment', [NewInstructorController::class, 'removeAssessments']);
        Route::post('k12connections/ajaxCheckAndUpdateProfilStatus/{instructor}/{user}', [NewInstructorController::class, "ajaxCheckAndUpdateProfilStatus"]);
        Route::post('k12connections/ajaxLaterUpdateProfilStatus/{instructor}/{user}', [NewInstructorController::class, "ajaxLaterUpdateProfilStatus"]);
        Route::post('k12connections/ajaxSubmitAndUpdateProfilStatus/{instructor}/{user}', [NewInstructorController::class, "ajaxSubmitAndUpdateProfilStatus"]);
        Route::post('/additional_certificate_all_category', [NewInstructorController::class, "additional_certificate_all_category"]);
        Route::post('/get_all_sub_category', [NewInstructorController::class, "get_all_sub_category"]);
        Route::post('k12connections/remove-recording-video', [NewInstructorController::class, 'removeRecordingVideo']);
        Route::post("k12connections/sampleLesssonSubjects",[NewInstructorController::class,"sampleLesssonSubjects"])->name("sampleLesssonSubjects");
        Route::post("/k12connections/submit_sample_lesson",[NewInstructorController::class,"submit_sample_lesson"])->name("submit_sample_lesson");
        // Angular Support routes
        Route::get("k12connections/session-logout", [UserProfileController::class, "sessionLogout"])->name('sessionLogout');
        Route::get("k12connections/profile", [UserProfileController::class, "instructorProfile"])->name('instructorProfile');
        Route::get("k12connections/my-opportunities", [UserProfileController::class, "userOpportunities"])->name('userOpportunities');
        Route::get('k12connections/faq', [NewInstructorController::class, "instructorFaq"])->name('instructorFaq');
        Route::get('k12connections/notifications', [NewInstructorController::class, "instructorNotifications"])->name('instructorNotifications');
        Route::get('k12connections/messages', [NewInstructorController::class, "instructorMessages"])->name('instructor-messages');
        Route::get('s3-file', "WEB\HomeController@signedS3Url")->name('s3_file');
        // Manage-Requirement as Opportunities
        Route::apiResource('k12connections/opportunities', OpportunitiesController::class);
        Route::get('k12connections/opportunities/{id}/related', [MarketPlaceOpportunitiesController::class, 'relatedOpportunities'])->name('relatedOpportunities');
        Route::get('k12connections/update-opportunity-preference', [MarketPlaceOpportunitiesController::class, 'setUserOpportunityPreference'])->name('updateOpportunityPreference');
        Route::post('k12connections/opportunities/send-apply', [MarketPlaceOpportunitiesController::class, 'sendApply'])->name('sendApply');
        Route::get('k12connections/opportunities/withdraw/{id}', [MarketPlaceOpportunitiesController::class, 'withdraw'])->name('withdraw');
    });

    Route::get("/k12connections/video-copy", [NewInstructorController::class, "assignVideoToInstructor"]);
//Admin