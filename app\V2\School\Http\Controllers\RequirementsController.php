<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PlatformSchoolRequirements;
use App\V2\Core\Helpers\ApiResponse;
use App\Schools;
use App\Models\SchoolReviewApplicants;
use App\Models\PlatformSchoolCalendersModel;
use App\Models\PlatformSchoolInvites;
use App\NotificationRequirement;
use App\OnboardingInstructor;
use App\ShortlistInstructorModel;
use App\SchoolRequirementContract;
use App\SchoolRequirementContractVersion;
use Exception;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class RequirementsController extends Controller
{
    // *************Get-Requirement-By-Id*************
    public function getRequirementById($id)
    {
        try {
            $requirement = PlatformSchoolRequirements::with(['school:id,school_name', 'subject:id,title', 'classType'])->find($id);
            $requirement->subject_area_name = v1SubjectAreaName($requirement->subject_area_id);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }
            return ApiResponse::success($requirement, "Requirement fetched successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch requirement: " . $e->getMessage(), 500);
        }
    }
    // *************Get-Requirement-By-Id*************

    // *************Get-Educator-By-Id*************
    public function getEducatorById($id)
    {
        try {
            $educator = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step2.otherExper', 'step2.references', 'step3.subjects','availability'])->find($id);
            $educator->step2->experience = $educator->step2->teching;
            unset($educator->step2->teching);
            $educator->step3->subjects->each(function ($subject) {
                $subject->subject_area_name = v1SubjectAreaName($subject->subject);
                $subject->sub_subject_name = v1SubjectName($subject->sub_subject);
            });
            if (!$educator) {
                return ApiResponse::error("Educator not found", 404);
            }
            return ApiResponse::success($educator, "Educator fetched successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch educator: " . $e->getMessage(), 500);
        }
    }
    // *************Get-Educator-By-Id*************

    public function getPostUtilities() {
        $school = auth()->user()->school;
        $schools = Schools::where(['district_id' => $school->district_id ])->get();
        $school_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'school')->first();
        $district_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'district')->first();
        return ApiResponse::success([
            'schools' => $schools,
            'school_calender' => $school_calender,
            'district_calender' => $district_calender
        ], "Post Utilities Fetched Successfully");
    }


    public function store(Request $request)
    {
        $data = [];
        $calender1 = [];
        $calender2 = [];
        $requirements = '';

        if (empty($request->requirementTitle)) {
            $data['requirement_type'] = $request->requirementType;
            $data['requirement_title'] = $request->requirementName;
            $data['school_id'] = $request->schoolName;
            $data['is_visible_to_school'] = 1;
            $data['class_type'] = $request->classType;
            $data['delivery_mode'] = !empty($request->deliveryMode) ? implode(',', $request->deliveryMode) : 'online';
            $data['subject_area_id'] = v1SubjectId($request->sub_subject);
            $data['subject_id'] = $request->sub_subject;
            $data['grade_levels_id'] = !empty($request->gradeLevels) ? implode(',', $request->gradeLevels) : null;
            $data['capacity'] = $request->numberOfStudents;
            $data['address'] = $request->address;
            $data['city'] = $request->city;
            $data['state'] = $request->state;
            $data['zip_code'] = $request->zipcode;
            $data['country'] = $request->country;
            $data['description'] = $request->requirementDescription;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }


        if (empty($request->id)) {
            $requirements = PlatformSchoolRequirements::insertGetId($data);
        }
        if (!empty($request->class_start_date)) {
            $data['start_date'] = Carbon::createFromFormat('m/d/Y', $request->class_start_date)->format('Y-m-d');
        }

        if (!empty($request->class_end_date)) {
            $data['end_date'] = Carbon::createFromFormat('m/d/Y', $request->class_end_date)->format('Y-m-d');
        }

        if (!empty($request->no_instrtructional_days)) {
            $data['no_instrtructional_days'] = $request->no_instrtructional_days;
        }

        if (!empty($request->class_duration)) {
            $data['class_duration'] = $request->class_duration;
        }

        if (!empty($request->no_non_instructional_hr)) {
            $data['no_non_instructional_hr'] = $request->no_non_instructional_hr;
        }

        if (!empty($request->scheduleType)) {
            $data['schedule_type'] = $request->scheduleType;
        }

        if (!empty($request->regular_days)) {
            $schedules = [];
            foreach (json_decode($request->regular_days) as $index => $day) {
                $schedules[] = [
                    'day' => $day,
                    'start_time' => $request->schedule_start_time ?? null,
                    'end_time' => $request->schedule_end_time ?? null,
                ];
            }
            $data['regular_days'] = json_encode($schedules);
            $data['schedule_1_days'] = null;
            $data['schedule_2_days'] = null;
        }

        if (!empty($request->schedule_1_days)) {
            $schedules = [];
            foreach (json_decode($request->schedule_1_days) as $index => $day) {
                $schedules[] = [
                    'day' => $day,
                    'start_time' => $request->schedule_1_start_time ?? null,
                    'end_time' => $request->schedule_1_end_time ?? null,
                ];
            }
            $data['schedule_1_days'] = json_encode($schedules);
            $data['regular_days'] = null;
        }

        if (!empty($request->schedule_2_days)) {
            $schedules = [];
            foreach (json_decode($request->schedule_2_days) as $index => $day) {
                $schedules[] = [
                    'day' => $day,
                    'start_time' => $request->schedule_2_start_time ?? null,
                    'end_time' => $request->schedule_2_end_time ?? null,
                ];
            }
            $data['schedule_2_days'] = json_encode($schedules);
        }

        if (!empty($request->sch_cal_screenshot)) {
            if ($request->hasFile('sch_cal_screenshot')) {
                $file = $request->file('sch_cal_screenshot');
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $name = time() . '-' . $originalName . '.' . $extension;
                $filename = 'uploads/school/calender' . $name;
                uploads3image($filename, $file);
            }
            $calender1['calender_url'] = $filename;
            $calender1['school_id'] = auth()->user()->id;
            $calender1['district_id'] = auth()->user()->district;
            $calender1['type'] = 'school';
            $calender1['lastUpdatedFrom'] = 'Requirement-' . $request->id;
            PlatformSchoolCalendersModel::create($calender1);
        }

        if (!empty($request->district_cal_screenshot)) {
            if ($request->hasFile('district_cal_screenshot')) {
                $file = $request->file('district_cal_screenshot');
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $name = time() . '-' . $originalName . '.' . $extension;
                $filename = 'uploads/' . $name;
                uploads3image($filename, $file);
            }
            $calender2['calender_url'] = $filename;
            $calender2['school_id'] = auth()->user()->id;
            $calender2['district_id'] = auth()->user()->district;
            $calender2['type'] = 'district';
            $calender2['lastUpdatedFrom'] = 'Requirement-' . $request->id;
            PlatformSchoolCalendersModel::create($calender2);
        }

        if ($request->has('teacher_schedule_screenshot')) {
            if ($request->hasFile('teacher_schedule_screenshot')) {
                $file = $request->file('teacher_schedule_screenshot');
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $name = time() . '-' . $originalName . '.' . $extension;
                $filename = 'uploads/' . $name;
                uploads3image($filename, $file);
            } else {
                $filename = $request->teacher_schedule_screenshot;
            }
            $data['teacher_schedule_screenshot'] = $filename;
        }

        if (!empty($request->other_sch_class_details)) {
            $data['class_details'] = $request->other_sch_class_details;
            $data['regular_days'] = null;
            $data['schedule_1_days'] = null;
            $data['schedule_2_days'] = null;
        }

        if (!empty($request->profileType_requirements)) {
            $data['profileType_requirements'] = $request->profileType_requirements;
        }

        if (!empty($request->language_requirements)) {
            $data['language_requirements'] = !empty($request->language_requirements) ? implode(',', $request->language_requirements) : null;
        }

        if (!empty($request->other_requirements)) {
            $data['other_requirements'] = $request->other_requirements;
        }

        if (!empty($request->states)) {
            $data['certifications_valid'] = !empty($request->states) ? implode(',', $request->states) : null;
        }

        if (!empty($request->min)) {
            if (!empty($request->min)) {
                $data['compensation_amount_min'] = floatval(preg_replace('/[^0-9.]/', '', $request->min));
            }
        }

        if (!empty($request->max)) {
            $data['compensation_amount_max'] = floatval(preg_replace('/[^0-9.]/', '', $request->max));
        }


        if (!empty($request->per_hour)) {
            $data['compensation_type'] = $request->per_hour;
        }

        if (!empty($request->benefits)) {
            $data['benefits'] = $request->benefits;
        }

        if (!empty($request->status)) {
            $data['status'] = $request->status;
        }

        if (!empty($request->will_choose_requiremnt)) {
            $data["will_choose_educator"] = $request->will_choose_requiremnt;
        }

        if (!empty($request->credential_type)) {
            $data["credential_check"] = $request->credential_type;
        }

        if (!empty($request->special_education)) {
            $data["special_education_certificate"] = $request->special_education;
        }

        if (!empty($request->provide_curriculum)) {
            $data["will_follow_provided_curriculum"] = $request->provide_curriculum;
        }

        if (!empty($request->access_to_schedule)) {
            $data["provide_schedule_access"] = $request->access_to_schedule;
        }
        if (!empty($request->qualification)) {
            $data['qualifications'] = $request->qualification;
        }
        if (!empty($request->experience)) {
            $data['experience'] = $request->experience;
        }


        if (!empty($request->total_budget)) {
            $data['total_budget'] = $request->total_budget;
        }

        if (!empty($request->min) && !empty($request->max) && !empty($request->per_hour) && !empty($request->benefits)) {
            $data['is_valid'] = '1';

            $subject_id = PlatformSchoolRequirements::where("id", $request->id)->value("subject_id");



            $user_ids_from_subject_table = DB::table("onboarding_instructor_subjects")
                ->where("sub_subject", $subject_id)
                ->pluck("user_id")
                ->toArray();


            $unique_user_id = [];
            $whizara_table_user_id = DB::table("onboarding_instructor_whizara_educator_contract")->whereIn("user_id", $user_ids_from_subject_table)->pluck("user_id")->toArray();

            $market_table_user_id = DB::table("onboarding_instructor_marketplace_educator_contract")->whereIn("user_id", $user_ids_from_subject_table)->pluck("user_id")->toArray();

            $unique_user_id = array_unique(array_merge($whizara_table_user_id, $market_table_user_id));

            $from_new_onboarding_instructor = DB::table("new_onboarding_instructor")->whereIn("id", $unique_user_id)->get();
            $count_result = $from_new_onboarding_instructor->count();
            $requirement_name = PlatformSchoolRequirements::where('id', $request->id)
                ->pluck('requirement_name')
                ->first();

            $content = "<span class='notification_requirement_count'>{$count_result}</span> educators matching your requirement <span class='notification_requirement_name'>{$requirement_name}</span> are available. Review educator profiles and invite them to apply.";
            $user_ids_json = json_encode($unique_user_id);
            $requirement_id = $request->id;


            // if (!empty($unique_user_id)) {

            //     $requirementnotfication = new NotificationRequirement();
            //     $requirementnotfication->content = $content;
            //     $requirementnotfication->requirement_id = $requirement_id;
            //     $requirementnotfication->user_ids = $user_ids_json;
            //     $requirementnotfication->school_id = auth()->user()->id;
            //     $requirementnotfication->save();
            // }
        } elseif (empty($request->min) && empty($request->max) && !empty($request->per_hour)) {
            $data['is_valid'] = '1';
        } else {
            $data['is_valid'] = '0';
        }


        if (!empty($request->id)) {
            $req = PlatformSchoolRequirements::find($request->id);
            $req->update($data);
        }
        $id = empty($request->id) ? (!empty($requirements) ? $requirements : null) : $request->id;

        if (empty($request->id) && empty($requirements)) {
            return response()->json(['success' => false, 'id' => $requirements ? $requirements: $request->id  ]);
        }
        return response()->json(['success' => true, 'id' => $requirements ? $requirements: $request->id ]);
    }

    public function duplicateRequirement($id)
    {
        $data = PlatformSchoolRequirements::find($id);

        if ($data) {
            $newData = $data->replicate();
            $newData->parent_id = $data->id;
            $newData->status = 'draft';
            $newData->is_valid = '0';
            $newData->save();

            return response()->json(['success' => true]);
        }

        return response()->json(['error' => 'Data not found']);
    }



    public function closeRequirement($id)
    {
        $data = PlatformSchoolRequirements::find($id);

        if ($data) {
            $data->update(['status' => 'closed']);

            return response()->json(['success' => true]);
        }

        return response()->json(['error' => 'Data not found']);
    }

    public function delete($id)
    {
        $data = PlatformSchoolRequirements::find($id);
        if ($data->delete()) {
            return response()->json(["success" => true, "message" => "Successfully Deleted"]);
        } else {
            return response()->json(["success" => false, "message" => "Requirement not found"]);
        }
    }

    // *************Review-Applicant-List*************
    public function reviewApplicantList($id)
    {
        try {
            $requirement = PlatformSchoolRequirements::find($id);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }
            $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6','user.shortList', 'school', 'requirement', 'invites'])->where('requirement_id', $id)->where('school_id', auth()->user()->school_id)->orderBy('id', 'DESC')->get();

            $educatorsData = [];
            foreach ($applicants as $applicant) {
                if (!$applicant->user) continue;
                $educator = $applicant->user;

                // Process education data
                $educationList = [];
                if ($educator->step2 && $educator->step2->education) {
                    foreach ($educator->step2->education as $certificate) {
                        $educationList[] = [
                            'education' => $certificate->education,
                            'certification_other' => $certificate->certification_other,
                            'states' => $certificate->states ? json_decode($certificate->states, true) : []
                        ];
                    }
                }

                // Process subjects data
                $subjects = [];
                if ($educator->step3 && $educator->step3->subjects) {
                    foreach ($educator->step3->subjects as $subject) {
                        $subjects[] = [
                            'subject_area_id' => $subject->subject,
                            'sub_subject_id' => $subject->sub_subject,
                            'subject_area_name' => $subject->subject ? v1SubjectAreaName($subject->subject) : null,
                            'sub_subject_name' => $subject->sub_subject ? v1SubjectName($subject->sub_subject) : null
                        ];
                    }
                }

                // Process grade levels
                $gradeLevels = [];
                if ($educator->step3 && $educator->step3->i_prefer_to_teach) {
                    $gradeIds = explode(',', $educator->step3->i_prefer_to_teach);
                    foreach ($gradeIds as $gradeId) {
                        $gradeLevels[] = [
                            'id' => trim($gradeId),
                            'name' => gradeLevel(trim($gradeId))
                        ];
                    }
                }

                // Check shortlist status
                $shortlistStatus = null;
                if ($educator->shortList) {
                    foreach ($educator->shortList as $shortList) {
                        if ($shortList->requirement_id == $id) {
                            $shortlistStatus = $shortList->status;
                            break;
                        }
                    }
                }

                // Build clean educator data structure
                $educatorData = [
                    // Basic Information
                    'id' => $educator->id,
                    'applicant_id' => $applicant->id,
                    'first_name' => $educator->first_name,
                    'last_name' => $educator->last_name,
                    'email' => $educator->email,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,
                    'user_status' => $educator->user_status,

                    // Location
                    'address' => [
                        'city' => $educator->city,
                        'state' => $educator->state,
                        'zipcode' => $educator->zipcode
                    ],

                    // Profile
                    'profile' => [
                        'title' => $educator->step5 ? $educator->step5->profile_title : null,
                        'description' => $educator->step5 ? $educator->step5->description : null,
                        'tags' => $educator->step5 && $educator->step5->profile_tags ? explode(',', $educator->step5->profile_tags) : [],
                        'video' => [
                            'url' => $educator->step5 && $educator->step5->video ? generateSignedUrl($educator->step5->video) : null,
                            'source' => $educator->step5 ? $educator->step5->video_source : null,
                            'processing' => $educator->step5 ? $educator->step5->processing_video : null
                        ]
                    ],

                    // Education & Certifications
                    'education' => $educationList,

                    // Teaching Preferences
                    'teaching_preferences' => [
                        'grade_levels' => $gradeLevels,
                        'subjects' => $subjects,
                        'format' => $educator->step3 ? $educator->step3->format : null,
                        'years_of_experience' => $educator->step2 ? $educator->step2->total_experience : null
                    ],

                    // Rates & Budget
                    'rates' => [
                        'proposed_rate' => $applicant->proposed_rate,
                        'online_rate' => $educator->onlinerate,
                        'inperson_rate' => $educator->inpersonrate,
                        'total_estimated_cost' => (float) $requirement->totalHours * $applicant->proposed_rate
                    ],

                    // Application Status
                    'application' => [
                        'status' => $applicant->status,
                        'created_at' => $applicant->created_at,
                        'shortlist_status' => $shortlistStatus // null, 0 (disliked), or 1 (liked)
                    ],

                    // Invites
                    'invites' => $applicant->invites,
                ];
                $educatorsData[] = $educatorData;
            }

            // Calculate totals
            $totalApplicants = count($educatorsData);
            $totalCost = 0;
            foreach ($educatorsData as $educator) {
                $totalCost += $educator['rates']['total_estimated_cost'];
            }

            return ApiResponse::success([
                'requirement' => [
                    'id' => $requirement->id,
                    'requirement_name' => $requirement->requirement_name,
                    'title' => $requirement->title,
                    'school_id' => $requirement->school_id,
                    'total_hours' => $requirement->totalHours
                ],
                'applicants' => $educatorsData,
                'summary' => [
                    'total_applicants' => $totalApplicants,
                    'total_estimated_cost' => $totalCost
                ]
            ], "Educators Fetched Successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch educators: " . $e->getMessage(), 500);
        }
    }
    // *************Review-Applicant-List*************

    // *************Like/Dislike Applicant*************
    public function likeDislikeApplicant(Request $request) {
        try {
            // Validate request
            $request->validate([
                'requirement_id' => 'required|integer|exists:platform_school_requirements,id',
                'user_id' => 'required|integer|exists:new_onboarding_instructor,id',
                'status' => 'nullable|integer|in:0,1' // 1 = like, 0 = dislike, null = neutral
            ]);

            $requirementId = $request->requirement_id;
            $userId = $request->user_id;
            $schoolId =  auth()->user()->school_id;
            $status = $request->status;

            // Check if requirement belongs to this school
            $requirement = PlatformSchoolRequirements::where('id', $requirementId)->where('school_id', $schoolId)->first();
            if (!$requirement) {
                return ApiResponse::error("Requirement not found or access denied", 404);
            }

            // Check if user has applied for this requirement
            $applicant = SchoolReviewApplicants::where('requirement_id', $requirementId)->where('school_id', $schoolId)->whereHas('user', function($query) use ($userId) {$query->where('id', $userId);})->first();
            if (!$applicant) {
                return ApiResponse::error("Applicant not found for this requirement", 404);
            }

            // Find existing shortlist record
            $shortlist = ShortlistInstructorModel::where(['requirement_id' => $requirementId, 'school_id' => $schoolId, 'user_id' => $userId])->first();
            if ($status === null) {
                if ($shortlist) {
                    $shortlist->delete();
                    $message = "Applicant removed from shortlist";
                } else {
                    $message = "Applicant was not in shortlist";
                }
            } else {
                // Add or update shortlist status
                if ($shortlist) {
                    $shortlist->update(['status' => $status, 'updated_by' => auth()->user()->id]);
                    $message = $status == 1 ? "Applicant liked successfully" : "Applicant disliked successfully";
                } else {
                    ShortlistInstructorModel::create([
                        'requirement_id' => $requirementId,
                        'school_id' => $schoolId,
                        'user_id' => $userId,
                        'status' => $status,
                        'updated_by' => auth()->user()->first_name . ' ' . auth()->user()->last_name
                    ]);
                    $message = $status == 1 ? "Applicant liked successfully" : "Applicant disliked successfully";
                }
            }

            // Get updated shortlist status
            $updatedShortlist = ShortlistInstructorModel::where([
                'requirement_id' => $requirementId,
                'school_id' => $schoolId,
                'user_id' => $userId
            ])->first();

            return ApiResponse::success([
                'requirement_id' => $requirementId,
                'user_id' => $userId,
                'shortlist_status' => $updatedShortlist ? $updatedShortlist->status : null,
                'is_liked' => $updatedShortlist && $updatedShortlist->status == 1,
                'is_disliked' => $updatedShortlist && $updatedShortlist->status == 0
            ], $message);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        } catch (Exception $e) {
            return ApiResponse::error("Failed to update shortlist status: " . $e->getMessage(), 500);
        }
    }
    // *************Like/Dislike Applicant*************

    // *************Check-Eligible-Educator*************
    private function checkEducatorMatch($educator, $requirement) {
        $reasons = [];
        $matches = true;

        // Check grade level match
        $educatorGradeLevels = explode(',', $educator->step3->i_prefer_to_teach ?? '');
        $requirementGradeLevels = explode(',', $requirement->grade_levels_id ?? '');
        $gradeMatch = count(array_intersect($educatorGradeLevels, $requirementGradeLevels)) > 0;

        if (!$gradeMatch) {
            $matches = false;
            $reasons[] = 'Grade levels do not match';
        }

        // Check subject match
        $educatorSubjects = $educator->step3->subjects->pluck('sub_subject')->toArray();
        $requirementSubjects = explode(',', $requirement->subject_id ?? '');
        $subjectMatch = count(array_intersect($educatorSubjects, $requirementSubjects)) > 0;

        if (!$subjectMatch) {
            $matches = false;
            $reasons[] = 'Subjects do not match';
        }

        // Check certification/state match
        $certificationMatch = false;
        if ($educator->step2 && $educator->step2->education && $educator->step2->education->count() > 0) {
            $certificationMatch = true;
        }
        if (!$certificationMatch) {
            $matches = false;
            $reasons[] = 'Educator does not have any certifications';
        }

        return [
            'matches' => $matches,
            'grade_match' => $gradeMatch,
            'subject_match' => $subjectMatch,
            'certification_match' => $certificationMatch,
            'reasons' => $reasons
        ];
    }
    // *************Check-Eligible-Educator*************

    // *************Get-Eligible-Applicant*************
    public function getEligibleApplicant(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'requirement_id' => 'required|exists:platform_school_requirements,id'
            ]);
            if ($validator->fails()) {
                return ApiResponse::error("Validation failed", 422, $validator->errors());
            }
            $data = $validator->validated();
            $requirementId = $data['requirement_id'];
            $schoolId = auth()->user()->school_id;

            $requirement = PlatformSchoolRequirements::where('id', $requirementId)->where('school_id', $schoolId)->first();
            if (!$requirement) {
                return ApiResponse::error("Requirement not found or access denied", 404);
            }

            // Fetch all active educators from new_onboarding_instructor table
            $educators = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6', 'shortList'])->whereIn('user_status', ['Active'])->orderBy('id', 'DESC')->get();
            $eligibleEducators = [];
            foreach ($educators as $educator) {
                // Check if educator matches the requirement criteria
                $matchResult = $this->checkEducatorMatch($educator, $requirement);

                $existingInvite = PlatformSchoolInvites::where('user_id', $educator->id)->where('requirement_id', $requirementId)->get();

                // Only include educators who match all requirements
                if ($matchResult['matches']) {
                    $educationList = [];
                    if ($educator->step2 && $educator->step2->education) {
                        foreach ($educator->step2->education as $certificate) {
                            $educationList[] = [
                                'education' => $certificate->education,
                                'certification_other' => $certificate->certification_other,
                                'states' => $certificate->states ? json_decode($certificate->states, true) : []
                            ];
                        }
                    }

                    $subjects = [];
                    if ($educator->step3 && $educator->step3->subjects) {
                        foreach ($educator->step3->subjects as $subject) {
                            $subjects[] = [
                                'subject_area_id' => $subject->subject,
                                'sub_subject_id' => $subject->sub_subject,
                                'subject_area_name' => $subject->subject ? v1SubjectAreaName($subject->subject) : null,
                                'sub_subject_name' => $subject->sub_subject ? v1SubjectName($subject->sub_subject) : null
                            ];
                        }
                    }

                    $gradeLevels = [];
                    if ($educator->step3 && $educator->step3->i_prefer_to_teach) {
                        $gradeIds = explode(',', $educator->step3->i_prefer_to_teach);
                        foreach ($gradeIds as $gradeId) {
                            $gradeLevels[] = [
                                'id' => trim($gradeId),
                                'name' => gradeLevel(trim($gradeId))
                            ];
                        }
                    }

                    $shortlistStatus = null;
                    if ($educator->shortList) {
                        foreach ($educator->shortList as $shortList) {
                            if ($shortList->requirement_id == $requirementId) {
                                $shortlistStatus = $shortList->status;
                                break;
                            }
                        }
                    }

                    // Build clean educator data structure exactly like reviewApplicantList
                    $educatorData = [
                        // Basic Information
                        'id' => $educator->id,
                        'first_name' => $educator->first_name,
                        'last_name' => $educator->last_name,
                        'email' => $educator->email,
                        'image' => $educator->image ? generateSignedUrl($educator->image) : null,
                        'user_status' => $educator->user_status,

                        // Location
                        'address' => [
                            'city' => $educator->city,
                            'state' => $educator->state,
                            'zipcode' => $educator->zipcode
                        ],

                        // Profile
                        'profile' => [
                            'title' => $educator->step5 ? $educator->step5->profile_title : null,
                            'description' => $educator->step5 ? $educator->step5->description : null,
                            'tags' => $educator->step5 && $educator->step5->profile_tags ? explode(',', $educator->step5->profile_tags) : [],
                            'video' => [
                                'url' => $educator->step5 && $educator->step5->video ? generateSignedUrl($educator->step5->video) : null,
                                'source' => $educator->step5 ? $educator->step5->video_source : null,
                                'processing' => $educator->step5 ? $educator->step5->processing_video : null
                            ]
                        ],

                        // Education & Certifications
                        'education' => $educationList,

                        // Teaching Preferences
                        'teaching_preferences' => [
                            'grade_levels' => $gradeLevels,
                            'subjects' => $subjects,
                            'format' => $educator->step3 ? $educator->step3->format : null,
                            'years_of_experience' => $educator->step2 ? $educator->step2->total_experience : null
                        ],

                        // Rates & Budget
                        'rates' => [
                            'proposed_rate' => $educator->onlinerate, // Using online rate as proposed rate
                            'online_rate' => $educator->onlinerate,
                            'inperson_rate' => $educator->inpersonrate,
                            'total_estimated_cost' => (float) $requirement->totalHours * ($educator->onlinerate ?? 0)
                        ],

                        // Application Status
                        'application' => [
                            'status' => 'eligible', // Since these are eligible educators
                            'created_at' => $educator->created_at,
                            'shortlist_status' => $shortlistStatus // null, 0 (disliked), or 1 (liked)
                        ],

                        'invites' => $existingInvite,
                    ];
                    $eligibleEducators[] = $educatorData;
                }
            }

            // Calculate totals exactly like reviewApplicantList
            $totalApplicants = count($eligibleEducators);
            $totalCost = 0;
            foreach ($eligibleEducators as $educator) {
                $totalCost += $educator['rates']['total_estimated_cost'];
            }

            return ApiResponse::success([
                'requirement' => [
                    'id' => $requirement->id,
                    'requirement_name' => $requirement->requirement_name,
                    'title' => $requirement->title,
                    'school_id' => $requirement->school_id,
                    'total_hours' => $requirement->totalHours
                ],
                'applicants' => $eligibleEducators,
                'summary' => [
                    'total_applicants' => $totalApplicants,
                    'total_estimated_cost' => $totalCost
                ]
            ], "Educators Fetched Successfully");
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch eligible educators: " . $e->getMessage(), 500);
        }
    }
    // *************Get-Eligible-Applicant*************

    // *************Invite-Applicant*************
    public function inviteApplicant(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'educator_ids'   => 'required|array',
                'educator_ids.*' => 'exists:new_onboarding_instructor,id',
                'requirement_id' => 'required|exists:platform_school_requirements,id',
                'deadline_date'  => 'required|date',
                'deadline_time'  => 'required',
                'timezone'       => 'nullable',
            ]);
            if ($validator->fails()) {
                return ApiResponse::error("Validation failed", 422, $validator->errors());
            }

            $data = $validator->validated();
            $schoolId  = auth()->user()->school_id;
            $invitedBy = auth()->id() ?? null;

            $requirement = PlatformSchoolRequirements::where('id', $data['requirement_id'])->where('school_id', $schoolId)->first();
            if (!$requirement) {
                return ApiResponse::error("Requirement not found or access denied", 404);
            }

            $deadlineDate = Carbon::createFromFormat('m/d/Y', $data['deadline_date'])->format('Y-m-d');
            $deadlineTime = Carbon::createFromFormat('h:i A', $data['deadline_time'])->format('H:i:s');
            $count = 0;

            $processedInvites = [];
            foreach ($data['educator_ids'] as $educatorId) {
                $applicant = SchoolReviewApplicants::where('requirement_id', $data['requirement_id'])->where('school_id', $schoolId)->whereHas('user', function ($query) use ($educatorId) {
                        $query->where('id', $educatorId);
                    })->first();
                if (!$applicant) {
                    continue;
                }

                $existingInvite = PlatformSchoolInvites::where('user_id', $educatorId)->where('requirement_id', $data['requirement_id'])->where('status', '!=', 'expired')->first();
                if ($existingInvite) {
                    $existingInvite->update([
                        'school_id'     => $schoolId,
                        'deadline_date' => $deadlineDate,
                        'deadline_time' => $deadlineTime,
                        'invited_by'    => $invitedBy,
                        'updated_at'    => now(),
                    ]);
                    $processedInvites[] = $existingInvite->fresh(); // push updated record
                } else {
                    $newInvite = PlatformSchoolInvites::create([
                        'user_id'        => $educatorId,
                        'requirement_id' => $data['requirement_id'],
                        'school_id'      => $schoolId,
                        'deadline_date'  => $deadlineDate,
                        'deadline_time'  => $deadlineTime,
                        'invited_by'     => $invitedBy,
                        'status'         => 'pending',
                        'created_at'     => now(),
                        'updated_at'     => now(),
                    ]);
                    $processedInvites[] = $newInvite;
                }
                $count++;
            }
            return ApiResponse::success($processedInvites, $count . ' invite(s) processed successfully.');
        } catch (ValidationException $e) {
            Log::error('Error sending invite: ' . $e->getMessage());
            return ApiResponse::error("Validation failed", 422, $e->errors());
        } catch (Exception $e) {
            Log::error('Error sending invite: ' . $e->getMessage());
            return ApiResponse::error("Failed to send invite: " . $e->getMessage(), 500);
        }
    }
    // *************Invite-Applicant*************

    // *************Invite-History**************
    public function inviteHistory(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'requirement_id' => 'required|exists:platform_school_requirements,id'
            ]);
            if ($validator->fails()) {
                return ApiResponse::error("Validation failed", 422, $validator->errors());
            }
            $data = $validator->validated();
            $requirementId = $data['requirement_id'];
            $schoolId = auth()->user()->school_id;

            $requirement = PlatformSchoolRequirements::find($requirementId);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }

            $invites = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList'])->where('requirement_id', $requirementId)->where('school_id', $schoolId)->orderBy('id', 'DESC')->get();
            $inviteData = [];
            foreach ($invites as $invite) {
                if (!$invite->user) continue;
                $educator = $invite->user;

                // Process education data
                $educationList = [];
                if ($educator->step2 && $educator->step2->education) {
                    foreach ($educator->step2->education as $certificate) {
                        $educationList[] = [
                            'education' => $certificate->education,
                            'certification_other' => $certificate->certification_other,
                            'states' => $certificate->states ? json_decode($certificate->states, true) : []
                        ];
                    }
                }

                // Process subjects data
                $subjects = [];
                if ($educator->step3 && $educator->step3->subjects) {
                    foreach ($educator->step3->subjects as $subject) {
                        $subjects[] = [
                            'subject_area_id' => $subject->subject,
                            'sub_subject_id' => $subject->sub_subject,
                            'subject_area_name' => $subject->subject ? v1SubjectAreaName($subject->subject) : null,
                            'sub_subject_name' => $subject->sub_subject ? v1SubjectName($subject->sub_subject) : null
                        ];
                    }
                }

                // Process grade levels
                $gradeLevels = [];
                if ($educator->step3 && $educator->step3->i_prefer_to_teach) {
                    $gradeIds = explode(',', $educator->step3->i_prefer_to_teach);
                    foreach ($gradeIds as $gradeId) {
                        $gradeLevels[] = [
                            'id' => trim($gradeId),
                            'name' => gradeLevel(trim($gradeId))
                        ];
                    }
                }

                // Check shortlist status for this requirement
                $shortlistStatus = null;
                if ($educator->shortList) {
                    foreach ($educator->shortList as $shortList) {
                        if ($shortList->requirement_id == $requirementId) {
                            $shortlistStatus = $shortList->status;
                            break;
                        }
                    }
                }

                // Build invite data structure similar to reviewApplicantList
                $inviteDataItem = [
                    // Invite Information
                    'invite_id' => $invite->id,
                    'deadline_date' => $invite->deadline_date,
                    'deadline_time' => $invite->deadline_time,
                    'invite_status' => $invite->status,
                    'invited_at' => $invite->created_at,

                    // Basic Information
                    'id' => $educator->id,
                    'first_name' => $educator->first_name,
                    'last_name' => $educator->last_name,
                    'email' => $educator->email,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,
                    'user_status' => $educator->user_status,

                    // Location
                    'address' => [
                        'city' => $educator->city,
                        'state' => $educator->state,
                        'zipcode' => $educator->zipcode
                    ],

                    // Profile
                    'profile' => [
                        'title' => $educator->step5 ? $educator->step5->profile_title : null,
                        'description' => $educator->step5 ? $educator->step5->description : null,
                        'tags' => $educator->step5 && $educator->step5->profile_tags ? explode(',', $educator->step5->profile_tags) : [],
                        'video' => [
                            'url' => $educator->step5 && $educator->step5->video ? generateSignedUrl($educator->step5->video) : null,
                            'source' => $educator->step5 ? $educator->step5->video_source : null,
                            'processing' => $educator->step5 ? $educator->step5->processing_video : null
                        ]
                    ],

                    // Education & Certifications
                    'education' => $educationList,

                    // Teaching Preferences
                    'teaching_preferences' => [
                        'grade_levels' => $gradeLevels,
                        'subjects' => $subjects,
                        'format' => $educator->step3 ? $educator->step3->format : null,
                        'years_of_experience' => $educator->step2 ? $educator->step2->total_experience : null
                    ],

                    // Rates & Budget
                    'rates' => [
                        'online_rate' => $educator->onlinerate,
                        'inperson_rate' => $educator->inpersonrate,
                        'total_estimated_cost' => (float) $requirement->totalHours * ($educator->onlinerate ?? 0)
                    ],

                    // Application Status
                    'application' => [
                        'status' => 'invited',
                        'created_at' => $invite->created_at,
                        'shortlist_status' => $shortlistStatus // null, 0 (disliked), or 1 (liked)
                    ],
                ];
                $inviteData[] = $inviteDataItem;
            }

            // Calculate totals
            $totalInvites = count($inviteData);
            $totalCost = 0;
            foreach ($inviteData as $invite) {
                $totalCost += $invite['rates']['total_estimated_cost'];
            }

            return ApiResponse::success([
                'requirement' => [
                    'id' => $requirement->id,
                    'requirement_name' => $requirement->requirement_name,
                    'title' => $requirement->title,
                    'school_id' => $requirement->school_id,
                    'total_hours' => $requirement->totalHours
                ],
                'invites' => $inviteData,
                'summary' => [
                    'total_invites' => $totalInvites,
                    'total_estimated_cost' => $totalCost
                ]
            ], "Invite history fetched successfully");
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch invite history: " . $e->getMessage(), 500);
        }
    }
    // *************Invite-History**************

    // *************Withdraw-Invite*************
    public function withdrawInvite($invite_id)
    {
        try {
            $school_id = auth()->user()->school_id;
            $invite = PlatformSchoolInvites::find($invite_id)->whereHas('requirement', function($query) use ($school_id) {
                $query->where('school_id', $school_id);
            })->first();
            if (!$invite) {
                return ApiResponse::error("Invite not found", 404);
            }
            if($invite->status == 'pending'){
                $invite->status = 'withdraw';
                $invite->save();
                return ApiResponse::success($invite, "Invite withdrawn successfully");
            }else{
                return ApiResponse::error("Invite can not be withdrawn", 400);
            }
        } catch (Exception $e) {
            return ApiResponse::error("Failed to withdraw invite: " . $e->getMessage(), 500);
        }
    }
    // *************Withdraw-Invite*************

    // *************Get-All-Post-Requirements*************
    public function getAllPostRequirements()
    {
        try {
            $requirements = PlatformSchoolRequirements::with(['school:id,school_name', 'subject', 'classType'])->where('school_id', auth()->user()->school_id)->get();
            $requirements->each(function ($requirement) {
                $requirement->subject_area_name = v1SubjectAreaName($requirement->subject_area_id);
                $requirement->subject_name = v1SubjectName($requirement->subject_id);
                $requirement->grade_level_names = rtrim(gradeLevel($requirement->grade_levels_id), ',');
                $requirement->applied_applicants = $requirement->reviewApplicants()->count();
            });
            return ApiResponse::success($requirements, "Requirements fetched successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch requirements: " . $e->getMessage(), 500);
        }
    }
    // *************Get-All-Post-Requirements*************

    // *************Get-Post_requirement-Details*************
    public function getPostRequirementDetails($id)
    {
        try {
            $requirement = PlatformSchoolRequirements::with(['school:id,school_name', 'subject:id,title', 'classType'])->find($id);
            $requirement->subject_area_name = v1SubjectAreaName($requirement->subject_area_id);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }
            return ApiResponse::success($requirement, "Requirement fetched successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch requirement: " . $e->getMessage(), 500);
        }
    }
    // *************Get-Post_requirement-Details*************

    // *************Select-Educator*************
    public function selectEducator(Request $request)
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'applicant_id' => 'required|integer|exists:platform_school_review_applicants,id'
            ]);

            if ($validator->fails()) {
                return ApiResponse::error("Validation failed", 422, $validator->errors());
            }

            $applicantId = $request->applicant_id;
            $schoolId = auth()->user()->school_id;

            // Find the applicant record
            $applicant = SchoolReviewApplicants::with(['requirement', 'user'])->where('id', $applicantId)->where('school_id', $schoolId)->where('status', 'accepted')->first();

            if (!$applicant) {
                return ApiResponse::error("Applicant not found or access denied", 404);
            }

            // Check if requirement belongs to this school
            if ($applicant->requirement->school_id !== $schoolId) {
                return ApiResponse::error("Requirement not found or access denied", 404);
            }

            DB::beginTransaction();

            // Update requirement status to 'educator_selected'
            $applicant->requirement->update(['status' => 'educator_selected']);

            // Create school_requirement_contracts record with only requirement_id
            $contract = SchoolRequirementContract::create([
                'requirement_id' => $applicant->requirement_id,
                'status' => 'draft',
                'created_by_id' => auth()->user()->id,
                'created_by_type' => get_class(auth()->user()),
                'updated_by_id' => auth()->user()->id,
                'updated_by_type' => get_class(auth()->user()),
            ]);

            // Create school_requirement_contract_versions record with null values
            $contractVersion = SchoolRequirementContractVersion::create([
                'school_requirement_contract_id' => $contract->id,
                'file_url' => null,
                'version_number' => 'v1.0',
                'notes' => null,
                'created_by_id' => auth()->user()->id,
                'created_by_type' => get_class(auth()->user()),
                'updated_by_id' => auth()->user()->id,
                'updated_by_type' => get_class(auth()->user()),
            ]);

            DB::commit();

            return ApiResponse::success([
                'applicant' => $applicant,
                'contract' => $contract,
                'contract_version' => $contractVersion,
                'message' => 'Educator selected successfully'
            ], "Educator selected successfully");

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error selecting educator: ' . $e->getMessage(), [
                'applicant_id' => $request->applicant_id ?? null,
                'school_id' => auth()->user()->school_id ?? null,
                'trace' => $e->getTraceAsString()
            ]);
            return ApiResponse::error("Failed to select educator: " . $e->getMessage(), 500);
        }
    }
    // *************Select-Educator*************
}